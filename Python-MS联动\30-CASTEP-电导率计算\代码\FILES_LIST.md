# CASTEP 电导率计算工具 - 完整程序文件清单

## 📁 核心程序文件（10个）

### 主程序
- `professional_launcher.py` - 主程序启动器，统一入口
- `professional_interface.html` - 专业Web界面（推荐使用）

### 核心模块
- `castep_conductivity_calculator.py` - 电导率计算核心模块
- `material_analyzer.py` - 材料类型分析模块
- `visualization.py` - 可视化图表生成模块
- `result_manager.py` - 结果统一管理模块

### 配置和数据
- `config.json` - 程序配置文件
- `Cu.bands` - 示例数据文件（铜的能带数据）

### 文档
- `README.md` - 完整使用说明
- `QUICK_START.md` - 快速开始指南

## 🚀 使用方法

### 方法一：Web界面（推荐新手）
1. 双击打开 `professional_interface.html`
2. 选择 `.bands` 文件
3. 点击"开始完整分析"

### 方法二：命令行（推荐专家）
```bash
# 完整分析
python professional_launcher.py Cu.bands --material Cu --complete-analysis

# 基本计算
python professional_launcher.py Cu.bands --material Cu --basic-only

# 批量处理
python professional_launcher.py --batch-mode --input-dir ./
```

## 📊 结果输出

所有结果统一保存在 `CASTEP_Results` 目录：
```
CASTEP_Results/
└── calculations/
    └── [材料名_时间戳]/
        ├── input/          # 输入文件
        ├── output/         # 计算结果
        ├── plots/          # 图表文件
        └── reports/        # HTML报告
```

## 💻 系统要求

### Python环境
- Python 3.6+
- numpy
- matplotlib
- scipy
- seaborn

### 安装依赖
```bash
pip install numpy matplotlib scipy seaborn
```

## ✅ 程序特点

1. **简洁易用**：只有10个文件，结构清晰
2. **功能完整**：材料分析、电导率计算、可视化、报告生成
3. **结果统一**：所有输出集中管理，便于查找
4. **专业标准**：符合科研工具的使用习惯
5. **新手友好**：Web界面3步完成计算

## 🔧 文件依赖关系

```
professional_interface.html (用户界面)
    ↓
professional_launcher.py (主程序)
    ↓
├── castep_conductivity_calculator.py (计算核心)
├── material_analyzer.py (材料分析)
├── visualization.py (图表生成)
├── result_manager.py (结果管理)
└── config.json (配置文件)
```

## 📝 注意事项

1. 所有文件必须在同一目录下
2. 确保Python依赖包已安装
3. 结果会自动保存到 `CASTEP_Results` 目录
4. 支持 `.bands` 格式的CASTEP能带文件

---

**这就是完整的CASTEP电导率计算工具！**
**文件精简、功能完整、使用简单！**
