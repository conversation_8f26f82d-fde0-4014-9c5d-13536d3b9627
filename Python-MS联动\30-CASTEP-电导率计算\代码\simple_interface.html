<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CASTEP 电导率计算工具 - 命令生成器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.2em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .main-content {
            padding: 30px;
        }

        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #ecf0f1;
            border-radius: 10px;
            background: #fafafa;
        }

        .section h2 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.5em;
            border-bottom: 2px solid #3498db;
            padding-bottom: 8px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #34495e;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 10px;
            border: 2px solid #bdc3c7;
            border-radius: 6px;
            font-size: 14px;
        }

        .button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            text-align: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(52, 152, 219, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(39, 174, 96, 0.3);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
        }

        .btn-warning:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(243, 156, 18, 0.3);
        }

        .btn-info {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
        }

        .btn-info:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(23, 162, 184, 0.3);
        }

        .command-output {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 6px;
            margin-top: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            word-break: break-all;
            position: relative;
        }

        .copy-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #3498db;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .copy-btn:hover {
            background: #2980b9;
        }

        .instructions {
            background: #e8f5e8;
            border: 1px solid #27ae60;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
        }

        .instructions h3 {
            color: #27ae60;
            margin-bottom: 10px;
        }

        .instructions ol {
            margin-left: 20px;
        }

        .instructions li {
            margin-bottom: 5px;
        }

        .grid-2 {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        @media (max-width: 768px) {
            .grid-2 {
                grid-template-columns: 1fr;
            }
            
            .button-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔬 CASTEP 电导率计算工具</h1>
            <p>命令生成器 • 一键复制 • 简单易用</p>
        </div>

        <div class="main-content">
            <!-- 文件和参数设置 -->
            <div class="section">
                <h2>📁 基本设置</h2>
                <div class="grid-2">
                    <div class="form-group">
                        <label for="filename">文件名 (.bands)：</label>
                        <input type="text" id="filename" value="Cu.bands" placeholder="例如: Cu.bands">
                    </div>
                    <div class="form-group">
                        <label for="temperature">温度 (K)：</label>
                        <input type="number" id="temperature" value="300" min="1" max="2000">
                    </div>
                </div>
                <div class="grid-2">
                    <div class="form-group">
                        <label for="outputDir">输出目录：</label>
                        <input type="text" id="outputDir" value="results" placeholder="输出目录名">
                    </div>
                    <div class="form-group">
                        <label for="configFile">配置文件：</label>
                        <select id="configFile">
                            <option value="">默认配置</option>
                            <option value="test_config.json">测试配置</option>
                            <option value="custom_config.json">自定义配置</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- 基本计算功能 -->
            <div class="section">
                <h2>🧮 基本计算</h2>
                <div class="button-grid">
                    <button class="btn btn-primary" onclick="generateCommand('')">基本计算</button>
                    <button class="btn btn-info" onclick="generateCommand('--show-analysis')">计算+分析</button>
                    <button class="btn btn-success" onclick="generateCommand('-o ' + getOutputDir() + '/results.txt')">计算+保存</button>
                    <button class="btn btn-warning" onclick="generateCommand('-t ' + getTemperature())">自定义温度</button>
                </div>
            </div>

            <!-- 可视化功能 -->
            <div class="section">
                <h2>📊 可视化功能</h2>
                <div class="button-grid">
                    <button class="btn btn-info" onclick="generateCommand('--plot-bands --save-plots ' + getOutputDir())">能带结构图</button>
                    <button class="btn btn-info" onclick="generateCommand('--plot-conductivity --save-plots ' + getOutputDir())">电导率张量图</button>
                    <button class="btn btn-info" onclick="generateCommand('--plot-analysis --save-plots ' + getOutputDir())">材料分析图</button>
                    <button class="btn btn-primary" onclick="generateCommand('--plot --save-plots ' + getOutputDir())">所有图表</button>
                    <button class="btn btn-success" onclick="generateCommand('--report --save-plots ' + getOutputDir())">HTML报告</button>
                </div>
            </div>

            <!-- 高级功能 -->
            <div class="section">
                <h2>🚀 高级功能</h2>
                <div class="button-grid">
                    <button class="btn btn-warning" onclick="generateCompleteCommand()">完整分析</button>
                    <button class="btn btn-success" onclick="generateQuickStartCommand()">一键开始</button>
                    <button class="btn btn-info" onclick="generateTempComparisonCommands()">温度对比</button>
                    <button class="btn btn-primary" onclick="generateBatchCommands()">批量处理</button>
                </div>
            </div>

            <!-- 命令输出 -->
            <div class="section">
                <h2>💻 生成的命令</h2>
                <div class="command-output" id="commandOutput">
                    <button class="copy-btn" onclick="copyCommand()">复制</button>
                    <div id="commandText">点击上方按钮生成命令</div>
                </div>
            </div>

            <!-- 使用说明 -->
            <div class="instructions">
                <h3>📋 使用说明</h3>
                <ol>
                    <li>设置文件名和计算参数</li>
                    <li>点击功能按钮生成对应命令</li>
                    <li>复制生成的命令</li>
                    <li>在命令行中执行命令</li>
                    <li>查看生成的结果和图表</li>
                </ol>
                <p><strong>💡 提示：</strong>推荐使用"一键开始"功能进行完整分析</p>
            </div>
        </div>
    </div>

    <script>
        // 获取参数的辅助函数
        function getFilename() {
            return document.getElementById('filename').value || 'Cu.bands';
        }

        function getTemperature() {
            return document.getElementById('temperature').value || '300';
        }

        function getOutputDir() {
            return document.getElementById('outputDir').value || 'results';
        }

        function getConfigFile() {
            const config = document.getElementById('configFile').value;
            return config ? `--config ${config}` : '';
        }

        // 生成基本命令
        function generateCommand(options) {
            const filename = getFilename();
            const config = getConfigFile();

            let command = `python launcher.py ${filename}`;

            if (config) {
                command += ` ${config}`;
            }

            if (options) {
                command += ` ${options}`;
            }

            displayCommand(command);
        }

        // 生成完整分析命令
        function generateCompleteCommand() {
            const filename = getFilename();
            const temp = getTemperature();
            const outputDir = getOutputDir();
            const config = getConfigFile();

            let command = `python launcher.py ${filename} --show-analysis --plot --report --save-plots ${outputDir} -t ${temp}`;

            if (config) {
                command += ` ${config}`;
            }

            displayCommand(command);
        }

        // 生成一键开始命令
        function generateQuickStartCommand() {
            const filename = getFilename();
            const temp = getTemperature();
            const outputDir = getOutputDir();
            const config = getConfigFile();

            let command = `python launcher.py ${filename} --show-analysis --report --save-plots ${outputDir} -t ${temp}`;

            if (config) {
                command += ` ${config}`;
            }

            displayCommand(command);
        }

        // 生成温度对比命令
        function generateTempComparisonCommands() {
            const filename = getFilename();
            const outputDir = getOutputDir();
            const config = getConfigFile();
            const temperatures = [300, 500, 800];

            let commands = [];
            temperatures.forEach(temp => {
                let command = `python launcher.py ${filename} --plot-conductivity --save-plots ${outputDir}/temp_${temp}K -t ${temp}`;
                if (config) {
                    command += ` ${config}`;
                }
                commands.push(command);
            });

            displayCommand(commands.join('\n\n'));
        }

        // 生成批量处理命令
        function generateBatchCommands() {
            const outputDir = getOutputDir();
            const config = getConfigFile();

            let batchScript = `# 批量处理所有 .bands 文件\n`;
            batchScript += `# Windows (PowerShell):\n`;
            batchScript += `Get-ChildItem *.bands | ForEach-Object {\n`;
            batchScript += `    $name = $_.BaseName\n`;
            batchScript += `    python launcher.py $_.Name --report --save-plots "${outputDir}/$name"`;
            if (config) {
                batchScript += ` ${config}`;
            }
            batchScript += `\n}\n\n`;

            batchScript += `# Linux/Mac (Bash):\n`;
            batchScript += `for file in *.bands; do\n`;
            batchScript += `    name=$(basename "$file" .bands)\n`;
            batchScript += `    python launcher.py "$file" --report --save-plots "${outputDir}/$name"`;
            if (config) {
                batchScript += ` ${config}`;
            }
            batchScript += `\ndone`;

            displayCommand(batchScript);
        }

        // 显示命令
        function displayCommand(command) {
            document.getElementById('commandText').textContent = command;
        }

        // 复制命令到剪贴板
        function copyCommand() {
            const commandText = document.getElementById('commandText').textContent;

            if (commandText === '点击上方按钮生成命令') {
                alert('请先生成命令！');
                return;
            }

            navigator.clipboard.writeText(commandText).then(() => {
                const copyBtn = document.querySelector('.copy-btn');
                const originalText = copyBtn.textContent;
                copyBtn.textContent = '已复制!';
                copyBtn.style.background = '#27ae60';

                setTimeout(() => {
                    copyBtn.textContent = originalText;
                    copyBtn.style.background = '#3498db';
                }, 2000);
            }).catch(err => {
                // 备用复制方法
                const textArea = document.createElement('textarea');
                textArea.value = commandText;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);

                alert('命令已复制到剪贴板！');
            });
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设置默认命令显示
            displayCommand('python launcher.py Cu.bands --show-analysis --report --save-plots results');
        });
    </script>
</body>
</html>
