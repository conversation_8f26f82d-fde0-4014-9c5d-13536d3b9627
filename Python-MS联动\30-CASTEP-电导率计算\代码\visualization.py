#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
CASTEP 电导率计算可视化模块

提供能带结构、电导率张量、材料分析等可视化功能

作者：Claude AI助手
日期：2025-01-20
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.font_manager import FontProperties
import seaborn as sns
from typing import Dict, Any, Optional, Tuple
import os

# 设置绘图样式
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

class ConductivityVisualizer:
    """电导率计算可视化类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化可视化器
        
        参数:
            config: 配置字典
        """
        self.config = config
        self.setup_fonts()
        
    def setup_fonts(self):
        """设置中文字体"""
        font_paths = self.config.get('font_settings', {}).get('chinese_font_paths', [])
        
        self.font_loaded = False
        for font_path in font_paths:
            try:
                if os.path.exists(font_path):
                    self.chinese_font = FontProperties(fname=font_path)
                    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
                    plt.rcParams['axes.unicode_minus'] = False
                    self.font_loaded = True
                    break
            except Exception:
                continue
        
        if not self.font_loaded:
            print("警告: 无法加载中文字体，图表中文可能显示为方块")
            plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
    
    def plot_band_structure(self, calculator, save_path: Optional[str] = None, show: bool = True):
        """
        绘制能带结构图
        
        参数:
            calculator: ConductivityCalculator实例
            save_path: 保存路径
            show: 是否显示图形
        """
        if calculator.bands is None:
            print("错误: 没有能带数据可以绘制")
            return
        
        fig, ax = plt.subplots(figsize=(10, 8))
        
        # 创建k点路径（简化版）
        k_path = np.arange(calculator.n_kpoints)
        
        # 绘制所有能带
        for band_idx in range(calculator.n_bands):
            band_energies = calculator.bands[band_idx, :]
            ax.plot(k_path, band_energies, 'b-', alpha=0.7, linewidth=1.5)
        
        # 绘制费米能级
        ax.axhline(y=calculator.fermi_energy, color='red', linestyle='--', 
                  linewidth=2, label=f'费米能级 = {calculator.fermi_energy:.3f} eV')
        
        # 标记穿过费米面的能带
        if calculator.material_analysis and calculator.material_analysis['is_fermi_crossing']:
            metallic_bands = calculator.material_analysis['metallic_bands']
            for band_idx in metallic_bands:
                if band_idx < calculator.n_bands:
                    band_energies = calculator.bands[band_idx, :]
                    ax.plot(k_path, band_energies, 'r-', linewidth=2, alpha=0.8)
        
        # 设置图形属性
        ax.set_xlabel('k点索引', fontsize=12)
        ax.set_ylabel('能量 (eV)', fontsize=12)
        ax.set_title(f'能带结构 - {calculator.filename}', fontsize=14, fontweight='bold')
        ax.grid(True, alpha=0.3)
        ax.legend()
        
        # 添加材料信息文本框
        if calculator.material_analysis:
            info_text = f"材料类型: {calculator.material_type.value}\n"
            info_text += f"带隙: {calculator.material_analysis['band_gap']:.3f} eV\n"
            info_text += f"穿过费米面的能带数: {calculator.material_analysis['bands_at_fermi']}"
            
            ax.text(0.02, 0.98, info_text, transform=ax.transAxes, 
                   verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"能带结构图已保存到: {save_path}")
        
        if show:
            plt.show()
        else:
            plt.close()
    
    def plot_conductivity_tensor(self, sigma_tensor: np.ndarray, actual_conductivity: np.ndarray,
                                save_path: Optional[str] = None, show: bool = True):
        """
        绘制电导率张量可视化
        
        参数:
            sigma_tensor: 电导率张量 (σ/τ)
            actual_conductivity: 实际电导率张量
            save_path: 保存路径
            show: 是否显示图形
        """
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 10))
        
        # 1. 电导率张量热图 (σ/τ)
        im1 = ax1.imshow(sigma_tensor, cmap='viridis', aspect='equal')
        ax1.set_title('电导率张量 (σ/τ, S/m/s)', fontsize=12, fontweight='bold')
        ax1.set_xticks([0, 1, 2])
        ax1.set_yticks([0, 1, 2])
        ax1.set_xticklabels(['x', 'y', 'z'])
        ax1.set_yticklabels(['x', 'y', 'z'])
        
        # 添加数值标注
        for i in range(3):
            for j in range(3):
                ax1.text(j, i, f'{sigma_tensor[i, j]:.2e}', 
                        ha='center', va='center', color='white', fontweight='bold')
        
        plt.colorbar(im1, ax=ax1, shrink=0.8)
        
        # 2. 实际电导率张量热图
        im2 = ax2.imshow(actual_conductivity, cmap='plasma', aspect='equal')
        ax2.set_title('实际电导率张量 (S/m)', fontsize=12, fontweight='bold')
        ax2.set_xticks([0, 1, 2])
        ax2.set_yticks([0, 1, 2])
        ax2.set_xticklabels(['x', 'y', 'z'])
        ax2.set_yticklabels(['x', 'y', 'z'])
        
        # 添加数值标注
        for i in range(3):
            for j in range(3):
                ax2.text(j, i, f'{actual_conductivity[i, j]:.2e}', 
                        ha='center', va='center', color='white', fontweight='bold')
        
        plt.colorbar(im2, ax=ax2, shrink=0.8)
        
        # 3. 对角元素柱状图
        diagonal_sigma = np.diag(sigma_tensor)
        diagonal_actual = np.diag(actual_conductivity)
        
        x_pos = np.arange(3)
        width = 0.35
        
        ax3.bar(x_pos - width/2, diagonal_sigma, width, label='σ/τ (S/m/s)', alpha=0.8)
        ax3.bar(x_pos + width/2, diagonal_actual, width, label='实际σ (S/m)', alpha=0.8)
        
        ax3.set_xlabel('方向')
        ax3.set_ylabel('电导率')
        ax3.set_title('对角元素比较', fontsize=12, fontweight='bold')
        ax3.set_xticks(x_pos)
        ax3.set_xticklabels(['σ_xx', 'σ_yy', 'σ_zz'])
        ax3.legend()
        ax3.set_yscale('log')
        
        # 4. 各向异性分析
        eigenvalues, _ = np.linalg.eigh(sigma_tensor)
        eigenvalues = np.sort(np.abs(eigenvalues))
        
        ax4.pie(eigenvalues, labels=[f'λ₁={eigenvalues[0]:.2e}', 
                                   f'λ₂={eigenvalues[1]:.2e}', 
                                   f'λ₃={eigenvalues[2]:.2e}'],
               autopct='%1.1f%%', startangle=90)
        ax4.set_title('电导率主轴分布', fontsize=12, fontweight='bold')
        
        # 计算各向异性比
        if eigenvalues[0] > 0:
            aniso_ratio = eigenvalues[2] / eigenvalues[0]
            ax4.text(0, -1.3, f'各向异性比: {aniso_ratio:.2f}', 
                    ha='center', fontsize=10, fontweight='bold')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"电导率张量图已保存到: {save_path}")
        
        if show:
            plt.show()
        else:
            plt.close()
    
    def plot_material_analysis(self, calculator, save_path: Optional[str] = None, show: bool = True):
        """
        绘制材料分析图表
        
        参数:
            calculator: ConductivityCalculator实例
            save_path: 保存路径
            show: 是否显示图形
        """
        if not calculator.material_analysis:
            print("错误: 没有材料分析数据可以绘制")
            return
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(14, 10))
        
        analysis = calculator.material_analysis
        
        # 1. 费米能级与能带关系
        band_ranges = []
        for band_idx in range(calculator.n_bands):
            band_min = np.min(calculator.bands[band_idx])
            band_max = np.max(calculator.bands[band_idx])
            band_ranges.append((band_min, band_max))
        
        y_pos = np.arange(calculator.n_bands)
        for i, (band_min, band_max) in enumerate(band_ranges):
            color = 'red' if i in analysis['metallic_bands'] else 'blue'
            ax1.barh(y_pos[i], band_max - band_min, left=band_min, 
                    color=color, alpha=0.7, height=0.8)
        
        ax1.axvline(x=calculator.fermi_energy, color='green', linestyle='--', 
                   linewidth=3, label=f'费米能级 = {calculator.fermi_energy:.3f} eV')
        ax1.set_xlabel('能量 (eV)')
        ax1.set_ylabel('能带索引')
        ax1.set_title('费米能级与能带分布', fontweight='bold')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. 材料类型饼图
        material_colors = {'metal': 'gold', 'semiconductor': 'lightblue', 'insulator': 'lightcoral'}
        material_names = {'metal': '金属', 'semiconductor': '半导体', 'insulator': '绝缘体'}
        
        current_type = calculator.material_type.value
        sizes = [100 if t == current_type else 0 for t in ['metal', 'semiconductor', 'insulator']]
        colors = [material_colors[t] for t in ['metal', 'semiconductor', 'insulator']]
        labels = [material_names[t] for t in ['metal', 'semiconductor', 'insulator']]
        
        # 只显示当前材料类型
        if current_type in material_colors:
            ax2.pie([100], labels=[material_names[current_type]], 
                   colors=[material_colors[current_type]], autopct='%1.0f%%', startangle=90)
        
        ax2.set_title(f'材料类型: {material_names.get(current_type, current_type)}', fontweight='bold')
        
        # 3. 能带间隙分析
        if analysis['band_gap'] > 0:
            gap_categories = ['小带隙\n(<0.5 eV)', '中等带隙\n(0.5-3 eV)', '大带隙\n(>3 eV)']
            gap_value = analysis['band_gap']
            
            if gap_value < 0.5:
                category_idx = 0
            elif gap_value < 3.0:
                category_idx = 1
            else:
                category_idx = 2
            
            colors = ['lightgreen', 'orange', 'red']
            heights = [0, 0, 0]
            heights[category_idx] = gap_value
            
            bars = ax3.bar(gap_categories, [0.5, 3.0, 6.0], alpha=0.3, color=colors)
            ax3.bar(gap_categories[category_idx], gap_value, color=colors[category_idx], alpha=0.8)
            
            ax3.set_ylabel('带隙 (eV)')
            ax3.set_title(f'能带间隙分析: {gap_value:.3f} eV', fontweight='bold')
            ax3.text(category_idx, gap_value + 0.1, f'{gap_value:.3f} eV', 
                    ha='center', fontweight='bold')
        else:
            ax3.text(0.5, 0.5, '金属材料\n无带隙', ha='center', va='center', 
                    transform=ax3.transAxes, fontsize=16, fontweight='bold')
            ax3.set_title('能带间隙分析', fontweight='bold')
        
        # 4. 推荐参数对比
        if 'material_properties' in analysis:
            props = analysis['material_properties']
            
            param_names = ['典型电导率\n(S/m/s)', '弛豫时间\n(s)', '缩放因子']
            param_values = [
                props.get('typical_conductivity', 0),
                props.get('relaxation_time', 0),
                props.get('scaling_factor', 0)
            ]
            
            # 使用对数刻度显示
            log_values = [np.log10(max(v, 1e-20)) for v in param_values]
            
            bars = ax4.bar(param_names, log_values, color=['skyblue', 'lightgreen', 'salmon'])
            ax4.set_ylabel('log₁₀(数值)')
            ax4.set_title('推荐计算参数', fontweight='bold')
            
            # 添加实际数值标注
            for i, (bar, value) in enumerate(zip(bars, param_values)):
                height = bar.get_height()
                ax4.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                        f'{value:.2e}', ha='center', va='bottom', fontsize=8)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"材料分析图已保存到: {save_path}")
        
        if show:
            plt.show()
        else:
            plt.close()
    
    def create_comprehensive_report(self, calculator, sigma_tensor: np.ndarray, 
                                  actual_conductivity: np.ndarray, output_dir: str = "visualization_output"):
        """
        创建综合可视化报告
        
        参数:
            calculator: ConductivityCalculator实例
            sigma_tensor: 电导率张量
            actual_conductivity: 实际电导率张量
            output_dir: 输出目录
        """
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 生成所有图表
        print("正在生成可视化报告...")
        
        # 1. 能带结构图
        band_path = os.path.join(output_dir, "band_structure.png")
        self.plot_band_structure(calculator, save_path=band_path, show=False)
        
        # 2. 电导率张量图
        conductivity_path = os.path.join(output_dir, "conductivity_tensor.png")
        self.plot_conductivity_tensor(sigma_tensor, actual_conductivity, 
                                    save_path=conductivity_path, show=False)
        
        # 3. 材料分析图
        analysis_path = os.path.join(output_dir, "material_analysis.png")
        self.plot_material_analysis(calculator, save_path=analysis_path, show=False)
        
        # 4. 创建HTML报告
        html_path = os.path.join(output_dir, "report.html")
        self._create_html_report(calculator, sigma_tensor, actual_conductivity, 
                                html_path, output_dir)
        
        print(f"综合可视化报告已生成到目录: {output_dir}")
        print(f"请打开 {html_path} 查看完整报告")
    
    def _create_html_report(self, calculator, sigma_tensor: np.ndarray, 
                           actual_conductivity: np.ndarray, html_path: str, output_dir: str):
        """创建HTML报告"""
        
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CASTEP 电导率计算报告</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }}
        .header {{ background-color: #f4f4f4; padding: 20px; border-radius: 5px; }}
        .section {{ margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }}
        .image-container {{ text-align: center; margin: 20px 0; }}
        .image-container img {{ max-width: 100%; height: auto; border: 1px solid #ddd; }}
        .data-table {{ width: 100%; border-collapse: collapse; margin: 10px 0; }}
        .data-table th, .data-table td {{ border: 1px solid #ddd; padding: 8px; text-align: center; }}
        .data-table th {{ background-color: #f2f2f2; }}
        .highlight {{ background-color: #ffffcc; padding: 5px; border-radius: 3px; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>CASTEP 电导率计算报告</h1>
        <p><strong>文件:</strong> {calculator.filename}</p>
        <p><strong>生成时间:</strong> {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>
    
    <div class="section">
        <h2>材料基本信息</h2>
        <table class="data-table">
            <tr><th>参数</th><th>数值</th></tr>
            <tr><td>材料类型</td><td class="highlight">{calculator.material_type.value}</td></tr>
            <tr><td>费米能级</td><td>{calculator.fermi_energy:.6f} eV</td></tr>
            <tr><td>能带数</td><td>{calculator.n_bands}</td></tr>
            <tr><td>k点数</td><td>{calculator.n_kpoints}</td></tr>
            <tr><td>电子数</td><td>{calculator.n_electrons}</td></tr>
            <tr><td>自旋分量</td><td>{calculator.n_spins}</td></tr>
        </table>
    </div>
    
    <div class="section">
        <h2>材料分析结果</h2>
        <div class="image-container">
            <img src="material_analysis.png" alt="材料分析图">
        </div>
        """
        
        if calculator.material_analysis:
            analysis = calculator.material_analysis
            html_content += f"""
        <table class="data-table">
            <tr><th>分析项目</th><th>结果</th></tr>
            <tr><td>估算带隙</td><td>{analysis['band_gap']:.6f} eV</td></tr>
            <tr><td>费米能级穿过能带</td><td>{'是' if analysis['is_fermi_crossing'] else '否'}</td></tr>
            <tr><td>穿过的能带数</td><td>{analysis['bands_at_fermi']}</td></tr>
            <tr><td>金属性能带</td><td>{analysis['metallic_bands']}</td></tr>
        </table>
            """
        
        html_content += f"""
    </div>
    
    <div class="section">
        <h2>能带结构</h2>
        <div class="image-container">
            <img src="band_structure.png" alt="能带结构图">
        </div>
    </div>
    
    <div class="section">
        <h2>电导率计算结果</h2>
        <div class="image-container">
            <img src="conductivity_tensor.png" alt="电导率张量图">
        </div>
        
        <h3>电导率张量 (σ/τ, S/m/s)</h3>
        <table class="data-table">
            <tr><th></th><th>x</th><th>y</th><th>z</th></tr>
            <tr><th>x</th><td>{sigma_tensor[0,0]:.4e}</td><td>{sigma_tensor[0,1]:.4e}</td><td>{sigma_tensor[0,2]:.4e}</td></tr>
            <tr><th>y</th><td>{sigma_tensor[1,0]:.4e}</td><td>{sigma_tensor[1,1]:.4e}</td><td>{sigma_tensor[1,2]:.4e}</td></tr>
            <tr><th>z</th><td>{sigma_tensor[2,0]:.4e}</td><td>{sigma_tensor[2,1]:.4e}</td><td>{sigma_tensor[2,2]:.4e}</td></tr>
        </table>
        
        <h3>实际电导率张量 (S/m)</h3>
        <table class="data-table">
            <tr><th></th><th>x</th><th>y</th><th>z</th></tr>
            <tr><th>x</th><td>{actual_conductivity[0,0]:.4e}</td><td>{actual_conductivity[0,1]:.4e}</td><td>{actual_conductivity[0,2]:.4e}</td></tr>
            <tr><th>y</th><td>{actual_conductivity[1,0]:.4e}</td><td>{actual_conductivity[1,1]:.4e}</td><td>{actual_conductivity[1,2]:.4e}</td></tr>
            <tr><th>z</th><td>{actual_conductivity[2,0]:.4e}</td><td>{actual_conductivity[2,1]:.4e}</td><td>{actual_conductivity[2,2]:.4e}</td></tr>
        </table>
        
        <p><strong>平均电导率:</strong> {np.trace(actual_conductivity)/3:.4e} S/m</p>
        """
        
        # 计算各向异性比
        eigenvalues, _ = np.linalg.eigh(sigma_tensor)
        eigenvalues = np.sort(np.abs(eigenvalues))
        if eigenvalues[0] > 0:
            aniso_ratio = eigenvalues[2] / eigenvalues[0]
            html_content += f"<p><strong>各向异性比:</strong> {aniso_ratio:.2f}</p>"
        
        html_content += """
    </div>
    
    <div class="section">
        <h2>计算说明</h2>
        <p>本报告基于CASTEP能带结构计算结果，使用Boltzmann输运理论计算电导率。</p>
        <p>电导率结果以σ/τ形式给出，其中τ为弛豫时间。实际电导率需要乘以材料特定的弛豫时间。</p>
        <p>材料类型通过费米能级与能带关系自动识别，计算策略根据材料类型自适应调整。</p>
    </div>
    
</body>
</html>
        """
        
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
