#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
CASTEP 电导率计算工具 API 服务器

提供HTTP API接口，支持前端直接调用计算功能

作者：Claude AI助手
日期：2025-01-20
"""

import os
import sys
import json
import threading
import time
import uuid
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import subprocess
from pathlib import Path
import traceback

# 导入计算模块
from professional_launcher import ProfessionalLauncher
from result_manager import ResultManager

class CalculationTask:
    """计算任务类"""
    def __init__(self, task_id, task_type, params):
        self.task_id = task_id
        self.task_type = task_type
        self.params = params
        self.status = 'pending'
        self.progress = 0
        self.message = '等待开始...'
        self.result = None
        self.error = None
        self.start_time = time.time()
        self.end_time = None

# 全局任务管理
active_tasks = {}
launcher = ProfessionalLauncher()

class APIHandler(BaseHTTPRequestHandler):
    """API请求处理器"""
    
    def do_OPTIONS(self):
        """处理CORS预检请求"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def do_GET(self):
        """处理GET请求"""
        parsed_path = urlparse(self.path)
        
        if parsed_path.path == '/':
            self.serve_file('professional_interface.html', 'text/html')
        elif parsed_path.path == '/status':
            query_params = parse_qs(parsed_path.query)
            task_id = query_params.get('task_id', [None])[0]
            self.handle_status_request(task_id)
        elif parsed_path.path == '/results':
            query_params = parse_qs(parsed_path.query)
            task_id = query_params.get('task_id', [None])[0]
            self.handle_results_request(task_id)
        elif parsed_path.path.startswith('/files/'):
            self.serve_result_file(parsed_path.path[7:])
        else:
            self.send_error(404, "Not found")
    
    def do_POST(self):
        """处理POST请求"""
        parsed_path = urlparse(self.path)
        
        if parsed_path.path == '/calculate':
            self.handle_calculation_request()
        elif parsed_path.path == '/upload':
            self.handle_file_upload()
        else:
            self.send_error(404, "Not found")
    
    def serve_file(self, filename, content_type):
        """提供静态文件服务"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                content = f.read()
            
            self.send_response(200)
            self.send_header('Content-type', content_type)
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(content.encode('utf-8'))
        except FileNotFoundError:
            self.send_error(404, f"File {filename} not found")
    
    def serve_result_file(self, filepath):
        """提供结果文件服务"""
        try:
            full_path = Path('CASTEP_Results') / filepath
            
            if not full_path.exists():
                self.send_error(404, f"File {filepath} not found")
                return
            
            # 确定内容类型
            if filepath.endswith('.html'):
                content_type = 'text/html'
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read().encode('utf-8')
            elif filepath.endswith('.png'):
                content_type = 'image/png'
                with open(full_path, 'rb') as f:
                    content = f.read()
            elif filepath.endswith('.json'):
                content_type = 'application/json'
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read().encode('utf-8')
            else:
                content_type = 'application/octet-stream'
                with open(full_path, 'rb') as f:
                    content = f.read()
            
            self.send_response(200)
            self.send_header('Content-type', content_type)
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(content)
            
        except Exception as e:
            self.send_error(500, f"Error serving file: {str(e)}")
    
    def handle_calculation_request(self):
        """处理计算请求"""
        try:
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))
            
            # 生成任务ID
            task_id = str(uuid.uuid4())
            
            # 创建计算任务
            task = CalculationTask(task_id, data.get('task_type', 'complete'), data)
            active_tasks[task_id] = task
            
            # 启动后台计算线程
            thread = threading.Thread(target=self.run_calculation, args=(task,))
            thread.daemon = True
            thread.start()
            
            # 返回任务ID
            self.send_json_response({
                'success': True,
                'task_id': task_id,
                'message': '计算任务已启动'
            })
            
        except Exception as e:
            self.send_json_response({
                'success': False,
                'error': str(e)
            }, 500)
    
    def run_calculation(self, task):
        """运行计算任务"""
        try:
            task.status = 'running'
            task.message = '正在初始化...'
            task.progress = 10
            
            params = task.params
            bands_file = params.get('filename')
            material_name = params.get('material_name', 'material')
            temperature = float(params.get('temperature', 300))
            task_type = params.get('task_type', 'complete')
            
            # 检查文件是否存在
            if not os.path.exists(bands_file):
                raise FileNotFoundError(f"文件不存在: {bands_file}")
            
            task.message = '开始计算...'
            task.progress = 20
            
            # 根据任务类型执行不同的计算
            if task_type == 'complete':
                task.message = '执行完整分析...'
                session_id = launcher.run_complete_analysis(
                    bands_file, material_name, temperature
                )
            elif task_type == 'basic':
                task.message = '执行基本计算...'
                session_id = launcher.run_basic_calculation(
                    bands_file, material_name, temperature
                )
            elif task_type == 'visualization':
                task.message = '生成可视化...'
                session_id = launcher.run_visualization_only(
                    bands_file, material_name, temperature
                )
            else:
                raise ValueError(f"未知的任务类型: {task_type}")
            
            task.progress = 90
            task.message = '生成结果报告...'
            
            # 获取结果信息
            session_info = launcher.result_manager.get_session_info(session_id)
            session_dir = launcher.result_manager.get_session_directory(session_id)
            
            # 构建结果路径
            result_paths = {
                'session_dir': str(session_dir.relative_to(Path.cwd())),
                'report_html': None,
                'plots': [],
                'data_files': []
            }
            
            # 查找生成的文件
            if (session_dir / 'reports' / 'session_report.html').exists():
                result_paths['report_html'] = str((session_dir / 'reports' / 'session_report.html').relative_to(Path.cwd()))
            
            plots_dir = session_dir / 'plots'
            if plots_dir.exists():
                for plot_file in plots_dir.glob('*.png'):
                    result_paths['plots'].append(str(plot_file.relative_to(Path.cwd())))
            
            output_dir = session_dir / 'output'
            if output_dir.exists():
                for data_file in output_dir.glob('*.json'):
                    result_paths['data_files'].append(str(data_file.relative_to(Path.cwd())))
            
            task.progress = 100
            task.status = 'completed'
            task.message = '计算完成！'
            task.result = {
                'session_id': session_id,
                'session_info': session_info,
                'result_paths': result_paths
            }
            task.end_time = time.time()
            
        except Exception as e:
            task.status = 'error'
            task.error = str(e)
            task.message = f'计算失败: {str(e)}'
            task.end_time = time.time()
            print(f"计算错误: {str(e)}")
            traceback.print_exc()
    
    def handle_status_request(self, task_id):
        """处理状态查询请求"""
        if not task_id or task_id not in active_tasks:
            self.send_json_response({
                'success': False,
                'error': '无效的任务ID'
            }, 404)
            return
        
        task = active_tasks[task_id]
        response = {
            'success': True,
            'task_id': task_id,
            'status': task.status,
            'progress': task.progress,
            'message': task.message
        }
        
        if task.status == 'completed':
            response['result'] = task.result
        elif task.status == 'error':
            response['error'] = task.error
        
        self.send_json_response(response)
    
    def handle_results_request(self, task_id):
        """处理结果查询请求"""
        if not task_id or task_id not in active_tasks:
            self.send_json_response({
                'success': False,
                'error': '无效的任务ID'
            }, 404)
            return
        
        task = active_tasks[task_id]
        if task.status != 'completed':
            self.send_json_response({
                'success': False,
                'error': '任务尚未完成'
            }, 400)
            return
        
        self.send_json_response({
            'success': True,
            'result': task.result
        })
    
    def handle_file_upload(self):
        """处理文件上传（简化实现）"""
        # 这里可以实现文件上传功能
        self.send_json_response({
            'success': False,
            'message': '文件上传功能待实现'
        })
    
    def send_json_response(self, data, status_code=200):
        """发送JSON响应"""
        json_data = json.dumps(data, ensure_ascii=False, indent=2)
        
        self.send_response(status_code)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json_data.encode('utf-8'))
    
    def log_message(self, format, *args):
        """自定义日志消息"""
        print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] {format % args}")


def start_server(port=8080):
    """启动API服务器"""
    server_address = ('', port)
    httpd = HTTPServer(server_address, APIHandler)
    
    print(f"🌐 CASTEP 电导率计算工具 API 服务器启动")
    print(f"📡 服务器地址: http://localhost:{port}")
    print(f"🔗 请在浏览器中打开上述地址")
    print(f"⏹️  按 Ctrl+C 停止服务器")
    print("="*50)
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
        httpd.server_close()


if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description='CASTEP 电导率计算工具 API 服务器')
    parser.add_argument('-p', '--port', type=int, default=8080, help='服务器端口 (默认: 8080)')
    
    args = parser.parse_args()
    start_server(args.port)
