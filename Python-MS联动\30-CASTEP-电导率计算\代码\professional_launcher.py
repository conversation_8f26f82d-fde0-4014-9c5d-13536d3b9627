#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
CASTEP 电导率计算工具专业启动器

统一结果管理，简化操作流程，专业用户友好

作者：Claude AI助手
日期：2025-01-20
"""

import os
import sys
import argparse
import subprocess
import json
from pathlib import Path
from result_manager import ResultManager
from castep_conductivity_calculator import ConductivityCalculator
from visualization import ConductivityVisualizer

class ProfessionalLauncher:
    """专业启动器"""
    
    def __init__(self):
        self.result_manager = ResultManager()
        self.current_session = None
        
    def run_complete_analysis(self, bands_file: str, material_name: str, 
                            temperature: float = 300.0, config_file: str = None):
        """运行完整分析流程"""
        print(f"🚀 开始完整分析: {material_name}")
        
        # 创建计算会话
        session_id = self.result_manager.create_calculation_session(
            material_name, 
            f"完整分析 - 温度: {temperature}K"
        )
        self.current_session = session_id
        
        try:
            # 1. 复制输入文件
            self.result_manager.add_file(session_id, bands_file, 'input', 
                                       'CASTEP能带结构文件')
            
            # 2. 执行计算
            print("📊 执行电导率计算...")
            calculator = ConductivityCalculator(config_file or 'config.json')
            calculator.read_bands_file(bands_file)
            sigma_tensor, actual_conductivity = calculator.calculate_conductivity(temperature)
            
            # 3. 保存计算结果
            self._save_calculation_results(session_id, calculator, sigma_tensor, actual_conductivity)
            
            # 4. 生成可视化
            print("🎨 生成可视化图表...")
            self._generate_visualizations(session_id, calculator, sigma_tensor, actual_conductivity)
            
            # 5. 生成报告
            print("📋 生成分析报告...")
            self._generate_reports(session_id, calculator, sigma_tensor, actual_conductivity)
            
            # 6. 更新会话状态
            results = {
                'material_type': calculator.material_type.value if calculator.material_type else 'unknown',
                'fermi_energy': calculator.fermi_energy,
                'band_gap': calculator.material_analysis['band_gap'] if calculator.material_analysis else 0,
                'avg_conductivity': float(np.trace(actual_conductivity) / 3) if actual_conductivity is not None else 0
            }
            
            self.result_manager.update_session_status(session_id, 'completed', results)
            
            print(f"✅ 分析完成！结果保存在: {self.result_manager.get_session_directory(session_id)}")
            return session_id
            
        except Exception as e:
            self.result_manager.update_session_status(session_id, 'error')
            print(f"❌ 分析失败: {str(e)}")
            raise
    
    def run_basic_calculation(self, bands_file: str, material_name: str, 
                            temperature: float = 300.0, config_file: str = None):
        """运行基本计算"""
        print(f"🧮 开始基本计算: {material_name}")
        
        session_id = self.result_manager.create_calculation_session(
            material_name, 
            f"基本计算 - 温度: {temperature}K"
        )
        
        try:
            # 复制输入文件
            self.result_manager.add_file(session_id, bands_file, 'input', 
                                       'CASTEP能带结构文件')
            
            # 执行计算
            calculator = ConductivityCalculator(config_file or 'config.json')
            calculator.read_bands_file(bands_file)
            sigma_tensor, actual_conductivity = calculator.calculate_conductivity(temperature)
            
            # 保存结果
            self._save_calculation_results(session_id, calculator, sigma_tensor, actual_conductivity)
            
            self.result_manager.update_session_status(session_id, 'completed')
            print(f"✅ 计算完成！结果保存在: {self.result_manager.get_session_directory(session_id)}")
            return session_id
            
        except Exception as e:
            self.result_manager.update_session_status(session_id, 'error')
            print(f"❌ 计算失败: {str(e)}")
            raise
    
    def run_visualization_only(self, bands_file: str, material_name: str, 
                             temperature: float = 300.0, config_file: str = None):
        """仅生成可视化"""
        print(f"🎨 生成可视化: {material_name}")
        
        session_id = self.result_manager.create_calculation_session(
            material_name, 
            f"可视化生成 - 温度: {temperature}K"
        )
        
        try:
            # 执行计算（需要先计算才能可视化）
            calculator = ConductivityCalculator(config_file or 'config.json')
            calculator.read_bands_file(bands_file)
            sigma_tensor, actual_conductivity = calculator.calculate_conductivity(temperature)
            
            # 生成可视化
            self._generate_visualizations(session_id, calculator, sigma_tensor, actual_conductivity)
            
            self.result_manager.update_session_status(session_id, 'completed')
            print(f"✅ 可视化完成！结果保存在: {self.result_manager.get_session_directory(session_id)}")
            return session_id
            
        except Exception as e:
            self.result_manager.update_session_status(session_id, 'error')
            print(f"❌ 可视化失败: {str(e)}")
            raise
    
    def run_temperature_comparison(self, bands_file: str, material_name: str, 
                                 temperatures: list, config_file: str = None):
        """温度对比分析"""
        print(f"🌡️ 温度对比分析: {material_name}")
        
        session_id = self.result_manager.create_calculation_session(
            material_name, 
            f"温度对比分析: {temperatures}"
        )
        
        try:
            results = {}
            for temp in temperatures:
                print(f"  计算温度: {temp}K")
                
                calculator = ConductivityCalculator(config_file or 'config.json')
                calculator.read_bands_file(bands_file)
                sigma_tensor, actual_conductivity = calculator.calculate_conductivity(temp)
                
                results[f"{temp}K"] = {
                    'temperature': temp,
                    'sigma_tensor': sigma_tensor.tolist(),
                    'actual_conductivity': actual_conductivity.tolist(),
                    'avg_conductivity': float(np.trace(actual_conductivity) / 3)
                }
            
            # 保存对比结果
            session_dir = self.result_manager.get_session_directory(session_id)
            results_file = session_dir / 'output' / 'temperature_comparison.json'
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            
            self.result_manager.add_file(session_id, str(results_file), 'output', 
                                       '温度对比分析结果', copy_file=False)
            
            self.result_manager.update_session_status(session_id, 'completed')
            print(f"✅ 温度对比完成！结果保存在: {session_dir}")
            return session_id
            
        except Exception as e:
            self.result_manager.update_session_status(session_id, 'error')
            print(f"❌ 温度对比失败: {str(e)}")
            raise
    
    def run_batch_processing(self, input_dir: str, output_dir: str = None):
        """批量处理"""
        print(f"📦 批量处理目录: {input_dir}")
        
        input_path = Path(input_dir)
        bands_files = list(input_path.glob("*.bands"))
        
        if not bands_files:
            print("❌ 未找到 .bands 文件")
            return
        
        print(f"找到 {len(bands_files)} 个文件")
        
        results = []
        for bands_file in bands_files:
            try:
                material_name = bands_file.stem
                print(f"\n处理: {material_name}")
                
                session_id = self.run_complete_analysis(
                    str(bands_file), material_name
                )
                results.append({
                    'file': str(bands_file),
                    'material': material_name,
                    'session_id': session_id,
                    'status': 'success'
                })
                
            except Exception as e:
                print(f"❌ 处理失败: {bands_file} - {str(e)}")
                results.append({
                    'file': str(bands_file),
                    'material': bands_file.stem,
                    'session_id': None,
                    'status': 'error',
                    'error': str(e)
                })
        
        # 保存批量处理结果
        batch_results_file = self.result_manager.base_dir / 'batch_results.json'
        with open(batch_results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"\n✅ 批量处理完成！结果汇总: {batch_results_file}")
        return results
    
    def _save_calculation_results(self, session_id: str, calculator, sigma_tensor, actual_conductivity):
        """保存计算结果"""
        session_dir = self.result_manager.get_session_directory(session_id)
        
        # 保存数值结果
        results = {
            'material_info': {
                'filename': calculator.filename,
                'fermi_energy': calculator.fermi_energy,
                'n_bands': calculator.n_bands,
                'n_kpoints': calculator.n_kpoints,
                'n_electrons': calculator.n_electrons,
                'n_spins': calculator.n_spins
            },
            'material_analysis': calculator.material_analysis,
            'conductivity_results': {
                'sigma_tensor': sigma_tensor.tolist(),
                'actual_conductivity': actual_conductivity.tolist(),
                'avg_conductivity': float(np.trace(actual_conductivity) / 3)
            }
        }
        
        results_file = session_dir / 'output' / 'calculation_results.json'
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        self.result_manager.add_file(session_id, str(results_file), 'output', 
                                   '计算结果数据', copy_file=False)
    
    def _generate_visualizations(self, session_id: str, calculator, sigma_tensor, actual_conductivity):
        """生成可视化图表"""
        session_dir = self.result_manager.get_session_directory(session_id)
        plots_dir = session_dir / 'plots'
        
        visualizer = ConductivityVisualizer(calculator.config)
        
        # 生成各种图表
        plots = [
            ('band_structure.png', lambda: visualizer.plot_band_structure(calculator, save_path=plots_dir / 'band_structure.png', show=False)),
            ('conductivity_tensor.png', lambda: visualizer.plot_conductivity_tensor(sigma_tensor, actual_conductivity, save_path=plots_dir / 'conductivity_tensor.png', show=False)),
            ('material_analysis.png', lambda: visualizer.plot_material_analysis(calculator, save_path=plots_dir / 'material_analysis.png', show=False))
        ]
        
        for plot_name, plot_func in plots:
            try:
                plot_func()
                self.result_manager.add_file(session_id, str(plots_dir / plot_name), 'plots', 
                                           f'{plot_name} 图表', copy_file=False)
            except Exception as e:
                print(f"⚠️ 生成 {plot_name} 失败: {str(e)}")
    
    def _generate_reports(self, session_id: str, calculator, sigma_tensor, actual_conductivity):
        """生成报告"""
        # 生成会话报告
        report_path = self.result_manager.generate_session_report(session_id)
        self.result_manager.add_file(session_id, report_path, 'reports', 
                                   '会话分析报告', copy_file=False)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='CASTEP电导率计算工具专业版',
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument('bands_file', nargs='?', help='CASTEP .bands文件路径')
    parser.add_argument('--material', type=str, help='材料名称')
    parser.add_argument('--temperature', type=float, default=300.0, help='计算温度 (K)')
    parser.add_argument('--config', type=str, help='配置文件路径')
    
    # 功能选项
    parser.add_argument('--complete-analysis', action='store_true', help='完整分析流程')
    parser.add_argument('--basic-only', action='store_true', help='仅基本计算')
    parser.add_argument('--with-analysis', action='store_true', help='计算+分析')
    parser.add_argument('--visualization-only', action='store_true', help='仅生成可视化')
    parser.add_argument('--temperature-comparison', type=str, help='温度对比 (逗号分隔)')
    
    # 批量处理
    parser.add_argument('--batch-mode', action='store_true', help='批量处理模式')
    parser.add_argument('--input-dir', type=str, default='./', help='输入目录')
    parser.add_argument('--output-dir', type=str, help='输出目录')
    
    args = parser.parse_args()
    
    launcher = ProfessionalLauncher()
    
    try:
        if args.batch_mode:
            launcher.run_batch_processing(args.input_dir, args.output_dir)
        elif args.temperature_comparison:
            if not args.bands_file:
                print("❌ 温度对比需要指定 .bands 文件")
                return 1
            
            temperatures = [float(t.strip()) for t in args.temperature_comparison.split(',')]
            material_name = args.material or Path(args.bands_file).stem
            launcher.run_temperature_comparison(args.bands_file, material_name, temperatures, args.config)
        elif args.bands_file:
            material_name = args.material or Path(args.bands_file).stem
            
            if args.complete_analysis:
                launcher.run_complete_analysis(args.bands_file, material_name, args.temperature, args.config)
            elif args.basic_only:
                launcher.run_basic_calculation(args.bands_file, material_name, args.temperature, args.config)
            elif args.visualization_only:
                launcher.run_visualization_only(args.bands_file, material_name, args.temperature, args.config)
            else:
                # 默认执行完整分析
                launcher.run_complete_analysis(args.bands_file, material_name, args.temperature, args.config)
        else:
            parser.print_help()
            print("\n💡 提示: 使用 professional_interface.html 获得更好的用户体验")
            return 1
            
    except Exception as e:
        print(f"❌ 执行失败: {str(e)}")
        return 1
    
    return 0


if __name__ == '__main__':
    import numpy as np
    sys.exit(main())
