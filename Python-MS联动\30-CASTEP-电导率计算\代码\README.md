# CASTEP 电导率计算工具 (改进版)

## 简介

本工具基于Materials Studio CASTEP模块的能带结构计算结果，实现了晶体材料电导率的计算。该工具使用Boltzmann输运理论计算电导率，考虑了温度效应和能带结构的影响。

**改进版特性：**
- 自动材料类型检测（金属/半导体/绝缘体）
- 可配置的计算参数，消除硬编码问题
- 根据材料类型自适应调整计算策略
- 支持各种材料类型的精确计算
- 详细的材料分析报告

## 功能特点

### 智能材料分析
- 自动检测费米能级是否穿过能带
- 估算能带间隙大小
- 识别材料类型（金属/半导体/绝缘体）
- 根据材料特性调整计算策略

### Boltzmann输运电导率计算
- 读取CASTEP .bands文件
- 自动解析晶胞和倒空间矢量信息
- 高精度群速度计算
- 完整电导率张量分析
- 考虑晶体各向异性
- 自动处理多种晶体结构

### 可配置参数系统
- 支持JSON配置文件
- 物理常数可配置
- 计算参数可调整
- 材料特性参数可定制

## 安装要求

- Python 3.6+
- 以下Python包：
  - numpy
  - matplotlib
  - scipy

## 文件结构

**核心模块：**
- `launcher.py`：主程序入口，处理命令行参数
- `castep_conductivity_calculator.py`：核心计算模块
- `material_analyzer.py`：材料类型分析模块
- `visualization.py`：可视化模块
- `config.json`：配置文件，包含所有可调参数

**Web界面：**
- `simple_interface.html`：简化Web界面（推荐）
- `web_interface.html`：完整Web界面
- `web_server.py`：Web服务器后端
- `start_web.py`：Web界面启动器

**文档：**
- `README.md`：主要使用说明
- `VISUALIZATION_GUIDE.md`：可视化功能指南
- `WEB_INTERFACE_GUIDE.md`：Web界面使用指南
- `IMPROVEMENTS_SUMMARY.md`：改进总结

## 使用方法

### 🌐 Web界面（推荐新用户）

**方法一：简化界面**
```bash
# 直接在浏览器中打开
start simple_interface.html  # Windows
open simple_interface.html   # Mac/Linux
```

**方法二：完整Web界面**
```bash
python start_web.py
# 自动打开浏览器访问 http://localhost:8080
```

### 💻 命令行方式

本工具采用命令行方式运行，基本使用格式：

```bash
python launcher.py 文件路径.bands [选项]
```

### 命令行选项

**基本选项：**
- `-t, --temperature`：指定计算温度，单位为K，如果未指定则使用配置文件中的默认值
- `-o, --output`：指定输出结果文件路径
- `-c, --config`：指定配置文件路径（默认：config.json）
- `--show-analysis`：显示详细的材料分析报告

**可视化选项：**
- `--plot`：显示所有可视化图表
- `--plot-bands`：仅显示能带结构图
- `--plot-conductivity`：仅显示电导率张量图
- `--plot-analysis`：仅显示材料分析图
- `--save-plots DIR`：保存图表到指定目录
- `--report`：生成完整的HTML可视化报告

### 使用示例

1. 使用默认配置计算电导率：
```bash
python launcher.py Cu.bands
```

2. 指定温度计算电导率：
```bash
python launcher.py Cu.bands -t 500
```

3. 计算并将结果保存到文件：
```bash
python launcher.py Cu.bands --temperature 300 --output results.txt
```

4. 使用自定义配置文件：
```bash
python launcher.py Cu.bands --config my_config.json
```

5. 显示详细材料分析：
```bash
python launcher.py Cu.bands --show-analysis
```

6. 显示可视化图表：
```bash
# 显示所有图表
python launcher.py Cu.bands --plot

# 仅显示能带结构
python launcher.py Cu.bands --plot-bands

# 显示电导率张量图
python launcher.py Cu.bands --plot-conductivity

# 显示材料分析图
python launcher.py Cu.bands --plot-analysis
```

7. 保存图表到文件：
```bash
# 保存所有图表到指定目录
python launcher.py Cu.bands --plot --save-plots output_dir

# 生成完整HTML报告
python launcher.py Cu.bands --report --save-plots report_dir
```

8. 组合使用：
```bash
# 完整分析+可视化+保存
python launcher.py Cu.bands --show-analysis --plot --save-plots results
```

### 计算结果

计算结果将显示在控制台，包括：

- **材料分析结果**：
  - 材料类型（金属/半导体/绝缘体）
  - 费米能级位置
  - 估算的能带间隙
  - 费米能级是否穿过能带
  - 推荐的计算参数

- **电导率计算结果**：
  - 电导率张量各分量 (σ/τ，单位: S/m/s)
  - 平均电导率
  - 电导率各向异性比
  - 根据材料类型确定的弛豫时间下的实际电导率

如果指定了输出文件，结果将同时保存到该文件中。

## 可视化功能

程序提供了丰富的可视化功能，帮助用户更好地理解计算结果：

### 1. 能带结构图
- 显示完整的能带结构
- 标记费米能级位置
- 高亮显示穿过费米面的金属性能带
- 显示材料类型和基本信息

### 2. 电导率张量可视化
- **热图显示**：电导率张量的完整3×3矩阵
- **对角元素比较**：σ_xx, σ_yy, σ_zz的柱状图对比
- **各向异性分析**：主轴分布饼图
- **数值标注**：所有张量元素的精确数值

### 3. 材料分析图表
- **费米能级与能带关系**：直观显示费米能级在能带中的位置
- **材料类型识别**：饼图显示识别的材料类型
- **能带间隙分析**：带隙大小的分类显示
- **推荐参数对比**：不同材料类型的计算参数对比

### 4. HTML综合报告
生成包含所有图表和数据的完整HTML报告：
- 材料基本信息表格
- 所有可视化图表
- 详细的计算结果表格
- 计算方法说明

### 可视化文件输出
```
output_directory/
├── band_structure.png          # 能带结构图
├── conductivity_tensor.png     # 电导率张量图
├── material_analysis.png       # 材料分析图
└── report.html                 # 完整HTML报告
```

## 配置文件说明

`config.json` 文件包含所有可配置的参数：

### 物理常数
```json
"physical_constants": {
  "hartree_to_ev": 27.211386,
  "hbar_eV_s": 6.582119569e-16,
  "angstrom_to_m": 1e-10,
  "elementary_charge": 1.602176634e-19,
  "boltzmann_constant": 1.380649e-23
}
```

### 计算参数
```json
"calculation_parameters": {
  "default_temperature": 300.0,
  "energy_margin": 5.0,
  "delta_k": 0.01,
  "max_thermal_energy_ratio": 10.0,
  "min_derivative_threshold": 1e-12
}
```

### 材料检测参数
```json
"material_detection": {
  "fermi_level_tolerance": 0.01,
  "band_gap_threshold": 0.1,
  "semiconductor_gap_range": [0.1, 4.0],
  "insulator_gap_threshold": 4.0
}
```

### 电导率估算参数
不同材料类型有不同的典型参数：
```json
"conductivity_estimation": {
  "metal": {
    "typical_conductivity": 6e21,
    "relaxation_time": 1e-14,
    "scaling_factor": 1.0
  },
  "semiconductor": {
    "typical_conductivity": 1e18,
    "relaxation_time": 5e-14,
    "scaling_factor": 1e-3
  },
  "insulator": {
    "typical_conductivity": 1e15,
    "relaxation_time": 1e-13,
    "scaling_factor": 1e-6
  }
}
```

## 输入文件要求

### CASTEP .bands文件
程序需要标准的CASTEP .bands文件，其中包含：
- k点网格及其权重
- 能带能量数据
- 晶胞信息和倒空间矢量
- 费米能级
- 自旋信息（如果有）

## 计算原理简介

本工具使用Boltzmann输运理论计算电导率，主要步骤包括：

1. **读取能带结构**：从CASTEP .bands文件中获取k点、能带能量、晶胞信息等

2. **计算群速度**：使用有限差分法计算能带群速度
   - v = (1/ħ) · (dE/dk)
   - 使用倒空间矢量基确保计算精度

3. **电导率计算**：基于Boltzmann输运方程
   - σ_αβ = e² · ∫ v_α v_β · (-df/dE) · δ(E-E_k) dk
   - 考虑温度对费米-狄拉克分布的影响
   - 计算完整的电导率张量，分析各向异性

4. **单位换算与归一化**：应用适当的物理常数和单位换算

## 材料类型自动检测

程序会自动分析材料类型并调整计算策略：

### 金属检测
- 检查费米能级是否穿过能带
- 计算穿过费米面的能带数量
- 使用较窄的能量窗口进行计算

### 半导体检测
- 估算能带间隙大小
- 考虑热激发效应
- 使用温度相关的计算策略

### 绝缘体检测
- 识别大带隙材料
- 使用热激活主导的计算方法
- 调整能量窗口范围

## 弛豫时间(τ)说明

程序会根据材料类型自动选择合适的弛豫时间：
- **金属材料**：τ ≈ 1×10⁻¹⁴ s
- **半导体材料**：τ ≈ 5×10⁻¹⁴ s
- **绝缘体材料**：τ ≈ 1×10⁻¹³ s

这些值可以在配置文件中调整，也可以通过与实验测量值对比来优化。

## 高级功能

### 自定义配置
用户可以创建自定义配置文件来适应特定的材料或计算需求：

```bash
cp config.json my_material_config.json
# 编辑 my_material_config.json
python launcher.py material.bands --config my_material_config.json
```

### 批量计算
可以编写脚本对多个材料进行批量计算：

```bash
for file in *.bands; do
    python launcher.py "$file" --output "${file%.bands}_results.txt"
done
```

## 注意事项

1. **计算精度**：结果准确性依赖于CASTEP计算质量，特别是k点网格密度
2. **各向异性**：对于各向异性材料，应关注完整电导率张量而非仅平均值
3. **材料特性**：程序会自动识别材料类型，但复杂材料可能需要手动调整参数
4. **温度效应**：不同温度下的计算结果会有显著差异，特别是对半导体材料
5. **弛豫时间**：实际应用中应根据实验数据或理论计算确定合适的弛豫时间

## 故障排除

### 常见问题
1. **导入错误**：确保所有Python依赖包已安装
2. **配置文件错误**：检查JSON格式是否正确
3. **内存不足**：对于大型系统，可能需要调整计算参数
4. **收敛问题**：尝试调整能量窗口或k点插值参数

### 调试模式
使用 `--show-analysis` 选项可以查看详细的材料分析信息，有助于理解计算过程和结果。

## 参考资料

- Madsen, G. K. H., & Singh, D. J. "BoltzTraP. A code for calculating band-structure dependent quantities." Computer Physics Communications 175, (2006).
- Mahan, G. D. "Good Thermoelectrics." Solid State Physics 51, (1998).
- 密度泛函理论计算电学性质的标准流程
- CASTEP用户手册：电子输运性质计算