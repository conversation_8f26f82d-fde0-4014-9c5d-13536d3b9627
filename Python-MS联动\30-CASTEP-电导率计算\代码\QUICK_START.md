# CASTEP 电导率计算工具 - 快速开始指南

## 📁 程序文件说明

**完整程序包含以下文件：**
- `professional_interface.html` - 用户界面（推荐使用）
- `professional_launcher.py` - 主程序
- `castep_conductivity_calculator.py` - 计算核心
- `material_analyzer.py` - 材料分析
- `visualization.py` - 图表生成
- `result_manager.py` - 结果管理
- `config.json` - 配置文件
- `Cu.bands` - 示例数据

## 🚀 新手用户 - 一键计算

### 第1步：启动程序
```bash
# 一键启动（推荐）
python start_server.py

# 或者手动启动API服务器
python api_server.py -p 8080
# 然后在浏览器中打开 http://localhost:8080
```

### 第2步：选择文件并计算
1. 点击"选择文件"，选择您的 `.bands` 文件
2. 输入材料名称（如：Cu, Si, GaAs）
3. 点击"开始完整分析"按钮
4. **程序自动开始计算，显示实时进度**

### 第3步：查看结果
- **实时进度**：界面显示计算进度和状态
- **在线查看**：计算完成后直接点击链接查看报告
- **文件管理**：所有结果统一保存在 `CASTEP_Results` 目录

```
CASTEP_Results/
└── calculations/
    └── [材料名_时间戳]/
        ├── input/          # 输入文件
        ├── output/         # 计算结果
        ├── plots/          # 图表文件
        └── reports/        # HTML报告
```

## 📋 结果文件说明

### 主要结果文件
- `reports/session_report.html` - **完整分析报告**（推荐查看）
- `output/calculation_results.json` - 数值计算结果
- `plots/band_structure.png` - 能带结构图
- `plots/conductivity_tensor.png` - 电导率张量图
- `plots/material_analysis.png` - 材料分析图

### 如何查看结果
1. **HTML报告**：用浏览器打开 `session_report.html`
2. **图表文件**：直接查看 PNG 图片
3. **数值结果**：用文本编辑器打开 JSON 文件

## 🔧 专家用户 - 高级功能

### 命令行使用
```bash
# 完整分析
python professional_launcher.py Cu.bands --material Cu --complete-analysis

# 仅基本计算
python professional_launcher.py Cu.bands --material Cu --basic-only

# 温度对比
python professional_launcher.py Cu.bands --material Cu --temperature-comparison 300,500,800

# 批量处理
python professional_launcher.py --batch-mode --input-dir ./
```

### 自定义配置
1. 复制 `config.json` 为 `custom_config.json`
2. 修改参数设置
3. 使用 `--config custom_config.json` 参数

## ❓ 常见问题

### Q: 找不到结果文件？
A: 所有结果都在 `CASTEP_Results` 目录中，按材料名和时间戳分类。

### Q: 计算失败怎么办？
A: 检查：
1. `.bands` 文件格式是否正确
2. Python依赖包是否安装完整
3. 查看错误信息进行排查

### Q: 如何理解计算结果？
A: 查看 `session_report.html` 报告，包含：
- 材料类型自动识别
- 电导率数值结果
- 可视化图表
- 详细分析说明

### Q: 可以处理多个文件吗？
A: 可以，使用批量处理功能：
1. 将所有 `.bands` 文件放在同一目录
2. 使用批量处理模式
3. 每个文件会生成独立的结果目录

## 💡 使用建议

### 新手用户
1. 使用Web界面，操作简单直观
2. 选择"完整分析"，获得最全面的结果
3. 重点查看HTML报告，包含所有重要信息

### 专业用户
1. 可以使用命令行进行批量处理
2. 自定义配置文件优化计算参数
3. 利用温度对比功能研究温度效应

### 结果管理
1. 所有结果统一保存，便于管理
2. 每次计算都有独立的会话目录
3. 可以导出特定会话的结果

## 📞 技术支持

如果遇到问题：
1. 查看 `README.md` 了解详细功能
2. 阅读 `VISUALIZATION_GUIDE.md` 了解可视化
3. 检查 `IMPROVEMENTS_SUMMARY.md` 了解最新改进

---

**记住：所有结果都在 `CASTEP_Results` 目录中！**
