#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
CASTEP 电导率计算工具 Web 界面启动器

一键启动Web界面，无需命令行操作

作者：Claude AI助手
日期：2025-01-20
"""

import os
import sys
import webbrowser
import time
import subprocess
import threading
from pathlib import Path

def check_dependencies():
    """检查依赖项"""
    print("🔍 检查依赖项...")
    
    required_files = [
        'launcher.py',
        'web_interface.html', 
        'web_server.py',
        'castep_conductivity_calculator.py',
        'material_analyzer.py',
        'visualization.py',
        'config.json'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    # 检查Python包
    try:
        import numpy
        import matplotlib
        import scipy
        print("✅ Python依赖包检查通过")
    except ImportError as e:
        print(f"❌ 缺少Python包: {e}")
        print("请安装: pip install numpy matplotlib scipy seaborn")
        return False
    
    print("✅ 所有依赖项检查通过")
    return True

def find_available_port(start_port=8080):
    """查找可用端口"""
    import socket
    
    for port in range(start_port, start_port + 100):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return port
        except OSError:
            continue
    
    return None

def start_server_background(port):
    """在后台启动服务器"""
    try:
        # 启动Web服务器
        subprocess.Popen([
            sys.executable, 'web_server.py', '-p', str(port)
        ], cwd=os.getcwd())
        return True
    except Exception as e:
        print(f"❌ 启动服务器失败: {e}")
        return False

def wait_for_server(port, timeout=10):
    """等待服务器启动"""
    import socket
    import time
    
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.settimeout(1)
                result = s.connect_ex(('localhost', port))
                if result == 0:
                    return True
        except:
            pass
        time.sleep(0.5)
    
    return False

def main():
    """主函数"""
    print("🚀 CASTEP 电导率计算工具 Web 界面启动器")
    print("="*50)
    
    # 检查依赖项
    if not check_dependencies():
        print("\n❌ 依赖项检查失败，无法启动")
        input("按回车键退出...")
        return
    
    # 查找可用端口
    print("\n🔍 查找可用端口...")
    port = find_available_port()
    if not port:
        print("❌ 无法找到可用端口")
        input("按回车键退出...")
        return
    
    print(f"✅ 找到可用端口: {port}")
    
    # 启动服务器
    print(f"\n🌐 启动Web服务器 (端口: {port})...")
    if not start_server_background(port):
        input("按回车键退出...")
        return
    
    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    if not wait_for_server(port):
        print("❌ 服务器启动超时")
        input("按回车键退出...")
        return
    
    print("✅ 服务器启动成功!")
    
    # 打开浏览器
    url = f"http://localhost:{port}"
    print(f"\n🌐 正在打开浏览器...")
    print(f"📡 访问地址: {url}")
    
    try:
        webbrowser.open(url)
        print("✅ 浏览器已打开")
    except Exception as e:
        print(f"⚠️ 无法自动打开浏览器: {e}")
        print(f"请手动在浏览器中打开: {url}")
    
    print("\n" + "="*50)
    print("🎉 CASTEP 电导率计算工具 Web 界面已启动!")
    print("📋 使用说明:")
    print("   1. 在Web界面中选择 .bands 文件")
    print("   2. 设置计算参数")
    print("   3. 点击相应按钮执行计算")
    print("   4. 查看生成的图表和报告")
    print("\n💡 提示:")
    print("   - 推荐使用'一键开始'功能")
    print("   - 所有结果会保存在指定目录")
    print("   - 可以随时生成HTML报告")
    print("\n⏹️ 关闭此窗口将停止Web服务器")
    print("="*50)
    
    try:
        # 保持程序运行
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n🛑 正在停止服务器...")
        print("✅ 已停止")

if __name__ == '__main__':
    main()
