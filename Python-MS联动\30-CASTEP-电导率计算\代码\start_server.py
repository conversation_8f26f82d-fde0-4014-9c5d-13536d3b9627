#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
CASTEP 电导率计算工具 - 一键启动脚本

自动启动API服务器并打开浏览器

作者：Claude AI助手
日期：2025-01-20
"""

import os
import sys
import time
import webbrowser
import subprocess
import socket
from pathlib import Path

def check_dependencies():
    """检查依赖项"""
    print("🔍 检查依赖项...")
    
    required_files = [
        'api_server.py',
        'professional_interface.html',
        'professional_launcher.py',
        'castep_conductivity_calculator.py',
        'material_analyzer.py',
        'visualization.py',
        'result_manager.py',
        'config.json'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    # 检查Python包
    try:
        import numpy
        import matplotlib
        import scipy
        print("✅ Python依赖包检查通过")
    except ImportError as e:
        print(f"❌ 缺少Python包: {e}")
        print("请安装: pip install numpy matplotlib scipy seaborn")
        return False
    
    print("✅ 所有依赖项检查通过")
    return True

def find_available_port(start_port=8080):
    """查找可用端口"""
    for port in range(start_port, start_port + 100):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return port
        except OSError:
            continue
    
    return None

def wait_for_server(port, timeout=10):
    """等待服务器启动"""
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.settimeout(1)
                result = s.connect_ex(('localhost', port))
                if result == 0:
                    return True
        except:
            pass
        time.sleep(0.5)
    
    return False

def main():
    """主函数"""
    print("🚀 CASTEP 电导率计算工具 - 一键启动")
    print("="*50)
    
    # 检查依赖项
    if not check_dependencies():
        print("\n❌ 依赖项检查失败，无法启动")
        input("按回车键退出...")
        return
    
    # 查找可用端口
    print("\n🔍 查找可用端口...")
    port = find_available_port()
    if not port:
        print("❌ 无法找到可用端口")
        input("按回车键退出...")
        return
    
    print(f"✅ 找到可用端口: {port}")
    
    # 启动API服务器
    print(f"\n🌐 启动API服务器 (端口: {port})...")
    try:
        # 启动服务器进程
        server_process = subprocess.Popen([
            sys.executable, 'api_server.py', '-p', str(port)
        ], cwd=os.getcwd())
        
        print("⏳ 等待服务器启动...")
        if not wait_for_server(port):
            print("❌ 服务器启动超时")
            server_process.terminate()
            input("按回车键退出...")
            return
        
        print("✅ 服务器启动成功!")
        
        # 打开浏览器
        url = f"http://localhost:{port}"
        print(f"\n🌐 正在打开浏览器...")
        print(f"📡 访问地址: {url}")
        
        try:
            webbrowser.open(url)
            print("✅ 浏览器已打开")
        except Exception as e:
            print(f"⚠️ 无法自动打开浏览器: {e}")
            print(f"请手动在浏览器中打开: {url}")
        
        print("\n" + "="*50)
        print("🎉 CASTEP 电导率计算工具已启动!")
        print("📋 使用说明:")
        print("   1. 在Web界面中选择 .bands 文件")
        print("   2. 设置材料名称和温度")
        print("   3. 点击'开始完整分析'按钮")
        print("   4. 等待计算完成，查看结果")
        print("\n💡 特色功能:")
        print("   - 一键计算：点击按钮直接开始计算")
        print("   - 实时进度：显示计算进度和状态")
        print("   - 结果管理：统一保存到 CASTEP_Results 目录")
        print("   - 在线查看：直接在浏览器中查看报告和图表")
        print("\n⏹️ 关闭此窗口将停止服务器")
        print("="*50)
        
        try:
            # 保持程序运行
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 正在停止服务器...")
            server_process.terminate()
            server_process.wait()
            print("✅ 服务器已停止")
            
    except Exception as e:
        print(f"❌ 启动服务器失败: {e}")
        input("按回车键退出...")

if __name__ == '__main__':
    main()
