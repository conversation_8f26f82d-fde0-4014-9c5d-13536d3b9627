#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
CASTEP 电导率计算工具 Web 服务器

提供HTTP接口，支持Web界面的后端功能

作者：Claude AI助手
日期：2025-01-20
"""

import os
import sys
import json
import subprocess
import threading
import time
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import webbrowser
from pathlib import Path

class ConductivityWebHandler(BaseHTTPRequestHandler):
    """Web请求处理器"""
    
    def do_GET(self):
        """处理GET请求"""
        parsed_path = urlparse(self.path)
        
        if parsed_path.path == '/' or parsed_path.path == '/index.html':
            self.serve_file('web_interface.html', 'text/html')
        elif parsed_path.path == '/status':
            self.serve_status()
        elif parsed_path.path.startswith('/results/'):
            self.serve_result_file(parsed_path.path[9:])  # 移除 '/results/' 前缀
        else:
            self.send_error(404, "File not found")
    
    def do_POST(self):
        """处理POST请求"""
        parsed_path = urlparse(self.path)
        
        if parsed_path.path == '/execute':
            self.handle_execute()
        elif parsed_path.path == '/upload':
            self.handle_upload()
        else:
            self.send_error(404, "Endpoint not found")
    
    def serve_file(self, filename, content_type):
        """提供静态文件服务"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                content = f.read()
            
            self.send_response(200)
            self.send_header('Content-type', content_type)
            self.send_header('Content-length', len(content.encode('utf-8')))
            self.end_headers()
            self.wfile.write(content.encode('utf-8'))
        except FileNotFoundError:
            self.send_error(404, f"File {filename} not found")
    
    def serve_status(self):
        """提供状态信息"""
        status = {
            'server_running': True,
            'available_files': self.get_available_files(),
            'recent_results': self.get_recent_results()
        }
        
        self.send_json_response(status)
    
    def serve_result_file(self, filepath):
        """提供结果文件服务"""
        try:
            full_path = os.path.join(os.getcwd(), filepath)
            
            if filepath.endswith('.html'):
                content_type = 'text/html'
            elif filepath.endswith('.png'):
                content_type = 'image/png'
            elif filepath.endswith('.txt'):
                content_type = 'text/plain'
            else:
                content_type = 'application/octet-stream'
            
            if filepath.endswith(('.png', '.jpg', '.jpeg')):
                with open(full_path, 'rb') as f:
                    content = f.read()
                self.send_response(200)
                self.send_header('Content-type', content_type)
                self.send_header('Content-length', len(content))
                self.end_headers()
                self.wfile.write(content)
            else:
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                self.send_response(200)
                self.send_header('Content-type', content_type)
                self.send_header('Content-length', len(content.encode('utf-8')))
                self.end_headers()
                self.wfile.write(content.encode('utf-8'))
                
        except FileNotFoundError:
            self.send_error(404, f"Result file {filepath} not found")
    
    def handle_execute(self):
        """处理执行请求"""
        try:
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))
            
            command = data.get('command', '')
            filename = data.get('filename', '')
            
            if not filename:
                self.send_json_response({'error': '未指定文件名'}, 400)
                return
            
            # 构建完整命令
            full_command = f"python launcher.py {filename} {command}"
            
            # 在后台执行命令
            def run_command():
                try:
                    result = subprocess.run(
                        full_command.split(),
                        capture_output=True,
                        text=True,
                        cwd=os.getcwd()
                    )
                    
                    # 保存执行结果
                    result_data = {
                        'command': full_command,
                        'returncode': result.returncode,
                        'stdout': result.stdout,
                        'stderr': result.stderr,
                        'timestamp': time.time()
                    }
                    
                    # 保存到结果文件
                    os.makedirs('web_results', exist_ok=True)
                    result_file = f"web_results/result_{int(time.time())}.json"
                    with open(result_file, 'w', encoding='utf-8') as f:
                        json.dump(result_data, f, ensure_ascii=False, indent=2)
                        
                except Exception as e:
                    print(f"命令执行错误: {e}")
            
            # 启动后台线程执行命令
            thread = threading.Thread(target=run_command)
            thread.daemon = True
            thread.start()
            
            self.send_json_response({
                'status': 'started',
                'command': full_command,
                'message': '命令已开始执行'
            })
            
        except Exception as e:
            self.send_json_response({'error': str(e)}, 500)
    
    def handle_upload(self):
        """处理文件上传"""
        # 简化的文件上传处理
        # 在实际应用中需要更完善的文件上传处理
        self.send_json_response({
            'status': 'success',
            'message': '文件上传功能需要进一步实现'
        })
    
    def send_json_response(self, data, status_code=200):
        """发送JSON响应"""
        json_data = json.dumps(data, ensure_ascii=False, indent=2)
        
        self.send_response(status_code)
        self.send_header('Content-type', 'application/json')
        self.send_header('Content-length', len(json_data.encode('utf-8')))
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json_data.encode('utf-8'))
    
    def get_available_files(self):
        """获取可用的.bands文件列表"""
        bands_files = []
        for file in os.listdir('.'):
            if file.endswith('.bands'):
                stat = os.stat(file)
                bands_files.append({
                    'name': file,
                    'size': stat.st_size,
                    'modified': stat.st_mtime
                })
        return bands_files
    
    def get_recent_results(self):
        """获取最近的计算结果"""
        results = []
        result_dirs = ['results', 'visualization_report', 'web_results']
        
        for result_dir in result_dirs:
            if os.path.exists(result_dir):
                for file in os.listdir(result_dir):
                    if file.endswith(('.html', '.png', '.txt', '.json')):
                        filepath = os.path.join(result_dir, file)
                        stat = os.stat(filepath)
                        results.append({
                            'name': file,
                            'path': filepath,
                            'size': stat.st_size,
                            'modified': stat.st_mtime
                        })
        
        # 按修改时间排序，最新的在前
        results.sort(key=lambda x: x['modified'], reverse=True)
        return results[:10]  # 返回最近10个结果
    
    def log_message(self, format, *args):
        """自定义日志消息"""
        print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] {format % args}")


def start_web_server(port=8080):
    """启动Web服务器"""
    server_address = ('', port)
    httpd = HTTPServer(server_address, ConductivityWebHandler)
    
    print(f"🌐 CASTEP 电导率计算工具 Web 服务器启动")
    print(f"📡 服务器地址: http://localhost:{port}")
    print(f"🔗 请在浏览器中打开上述地址")
    print(f"⏹️  按 Ctrl+C 停止服务器")
    print("="*50)
    
    # 自动打开浏览器
    try:
        webbrowser.open(f'http://localhost:{port}')
    except Exception as e:
        print(f"无法自动打开浏览器: {e}")
        print("请手动在浏览器中打开 http://localhost:{port}")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
        httpd.server_close()


if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description='CASTEP 电导率计算工具 Web 服务器')
    parser.add_argument('-p', '--port', type=int, default=8080, help='服务器端口 (默认: 8080)')
    
    args = parser.parse_args()
    
    # 检查必要文件是否存在
    required_files = ['launcher.py', 'web_interface.html', 'castep_conductivity_calculator.py']
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file in missing_files:
            print(f"   - {file}")
        print("请确保所有必要文件都在当前目录下")
        sys.exit(1)
    
    start_web_server(args.port)
