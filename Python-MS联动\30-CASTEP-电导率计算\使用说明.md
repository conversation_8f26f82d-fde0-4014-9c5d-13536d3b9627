# CASTEP电导率计算程序使用说明

## 简介
本程序用于基于CASTEP (Material Studio模块) 的电导率计算与可视化。程序能够从介电函数数据中计算电导率，并生成高质量的可视化图表和数据文件。

## 系统要求
- Python 3.8或更高版本
- 依赖库：numpy, matplotlib, scipy, pandas

## 安装
1. 确保已经安装Python 3.8+
2. 安装所需依赖库：
   ```
   pip install numpy matplotlib scipy pandas
   ```
3. 下载程序文件 `电导率计算.py`

## 数据格式要求
程序支持以下两种输入数据格式：

### 1. 标准数值格式
每行至少包含三列数据：能量(eV)、介电函数实部、介电函数虚部。例如：
```
0.01  1.234  0.000
0.02  1.235  0.001
...
```

### 2. Markdown表格格式
数据需以Markdown表格形式组织，表格需包含能量、介电函数实部和虚部，例如：
```
| 能量(eV) | 介电函数实部 | 介电函数虚部 |
| ------- | ----------- | ----------- |
| 0.01    | 1.234       | 0.000       |
| 0.02    | 1.235       | 0.001       |
...
```

## 使用方法

### 基本用法
直接运行程序：
```
python 电导率计算.py
```

程序默认会在以下位置寻找介电函数数据文件：
- `Python-MS联动/30-CASTEP-电导率计算/介电数据.md`
- `介电数据.md`（当前目录）
- `../介电数据.md`（上级目录）

如果找不到有效的数据文件，程序会自动生成模拟测试数据进行计算。

### 输出结果
程序会在 `results` 目录下生成以下文件：
- `电导率数据.csv`：包含能量、介电函数虚部和电导率数据的CSV文件
- `电导率分析.png`：包含介电函数和电导率可视化图表的PNG图像

## 高级用法
如需处理其他数据文件或修改输出路径，可以编辑程序开头的以下参数：
```python
# 在main函数中
epsilon_file = "Python-MS联动/30-CASTEP-电导率计算/介电数据.md"  # 修改为您的数据文件路径
output_csv = "results/电导率数据.csv"  # 修改为您期望的CSV输出路径
output_png = "results/电导率分析.png"  # 修改为您期望的图像输出路径
```

## 程序扩展
如果您希望批量处理多个材料的数据，可以参考以下思路扩展程序：
1. 创建一个目录，包含多个材料的介电函数数据文件
2. 编写一个批处理脚本，遍历所有数据文件并调用电导率计算程序
3. 将结果保存到按材料名称组织的子目录中

## 故障排除
1. **无法读取数据文件**：检查文件路径和编码，程序支持UTF-8、Latin-1和GBK编码
2. **图表中文显示为方块**：确保系统中安装了支持中文的字体，或修改程序中的字体设置
3. **计算结果异常**：检查输入数据的格式和单位，确保能量单位为eV，介电函数为无量纲值

## 参考文献
1. CASTEP用户指南，Materials Studio 2023
2. 计算材料学中的光学性质计算方法，J. Phys.: Condens. Matter 