<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CASTEP 电导率计算工具 - 专业版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.2em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            color: #7f8c8d;
            font-size: 1.1em;
        }

        .workflow {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            overflow: hidden;
        }

        .workflow-header {
            background: #3498db;
            color: white;
            padding: 20px;
            font-size: 1.3em;
            font-weight: 600;
        }

        .workflow-content {
            padding: 30px;
        }

        .step {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
            padding: 20px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            transition: all 0.3s;
        }

        .step:hover {
            border-color: #3498db;
            background: #f8f9fa;
        }

        .step-number {
            background: #3498db;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 20px;
            flex-shrink: 0;
        }

        .step-content {
            flex: 1;
        }

        .step-title {
            font-size: 1.2em;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .step-description {
            color: #7f8c8d;
            margin-bottom: 15px;
        }

        .step-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-1px);
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .input-group {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-bottom: 10px;
        }

        .input-group label {
            min-width: 80px;
            font-weight: 600;
            color: #2c3e50;
        }

        .input-group input, .input-group select {
            padding: 8px 12px;
            border: 2px solid #bdc3c7;
            border-radius: 4px;
            font-size: 14px;
            flex: 1;
        }

        .input-group input:focus, .input-group select:focus {
            outline: none;
            border-color: #3498db;
        }

        .results-panel {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .results-header {
            background: #27ae60;
            color: white;
            padding: 20px;
            font-size: 1.3em;
            font-weight: 600;
        }

        .results-content {
            padding: 30px;
        }

        .command-display {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 15px 0;
            position: relative;
            word-break: break-all;
        }

        .copy-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #3498db;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }

        .copy-btn:hover {
            background: #2980b9;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-ready { background: #27ae60; }
        .status-pending { background: #f39c12; }
        .status-error { background: #e74c3c; }

        .help-section {
            background: #e8f5e8;
            border: 1px solid #27ae60;
            border-radius: 8px;
            padding: 20px;
            margin-top: 30px;
        }

        .help-section h3 {
            color: #27ae60;
            margin-bottom: 15px;
        }

        .file-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
        }

        .mode-selector {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .mode-btn {
            padding: 10px 20px;
            border: 2px solid #3498db;
            background: white;
            color: #3498db;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .mode-btn.active {
            background: #3498db;
            color: white;
        }

        .expert-mode {
            display: none;
        }

        .expert-mode.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1>🔬 CASTEP 电导率计算工具</h1>
            <p>专业版 • 简洁高效 • 结果统一管理</p>
        </div>

        <!-- 模式选择 -->
        <div class="mode-selector">
            <button class="mode-btn active" onclick="switchMode('beginner')">新手模式</button>
            <button class="mode-btn" onclick="switchMode('expert')">专家模式</button>
        </div>

        <!-- 新手模式工作流程 -->
        <div id="beginnerMode" class="workflow">
            <div class="workflow-header">
                📋 推荐工作流程 - 三步完成计算
            </div>
            <div class="workflow-content">
                <!-- 步骤1：选择文件 -->
                <div class="step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <div class="step-title">选择计算文件</div>
                        <div class="step-description">选择您的 .bands 文件，设置材料名称</div>
                        <div class="input-group">
                            <label>文件:</label>
                            <input type="file" id="bandsFile" accept=".bands" onchange="handleFileSelect(this)">
                        </div>
                        <div class="input-group">
                            <label>材料名:</label>
                            <input type="text" id="materialName" placeholder="例如: Cu, Si, GaAs" value="Cu">
                        </div>
                        <div class="input-group">
                            <label>温度:</label>
                            <input type="number" id="temperature" value="300" min="1" max="2000">
                            <span>K</span>
                        </div>
                        <div id="fileInfo" class="file-info" style="display: none;">
                            <strong>已选择:</strong> <span id="fileName"></span><br>
                            <strong>大小:</strong> <span id="fileSize"></span>
                        </div>
                    </div>
                </div>

                <!-- 步骤2：一键计算 -->
                <div class="step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <div class="step-title">执行计算</div>
                        <div class="step-description">一键完成材料分析、电导率计算和可视化</div>
                        <div class="step-controls">
                            <button class="btn btn-primary" onclick="runCompleteAnalysis()">
                                <span class="status-indicator status-ready"></span>
                                开始完整分析
                            </button>
                            <button class="btn btn-secondary" onclick="runBasicCalculation()">
                                仅基本计算
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 步骤3：查看结果 -->
                <div class="step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <div class="step-title">查看结果</div>
                        <div class="step-description">所有结果统一保存在 CASTEP_Results 目录中</div>
                        <div class="step-controls">
                            <button class="btn btn-success" onclick="openResultsFolder()">
                                📁 打开结果文件夹
                            </button>
                            <button class="btn btn-primary" onclick="viewLatestReport()">
                                📋 查看最新报告
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 专家模式 -->
        <div id="expertMode" class="expert-mode">
            <div class="workflow">
                <div class="workflow-header">
                    🔧 专家模式 - 高级功能
                </div>
                <div class="workflow-content">
                    <div class="input-group">
                        <label>配置文件:</label>
                        <select id="configFile">
                            <option value="">默认配置</option>
                            <option value="test_config.json">测试配置</option>
                            <option value="custom_config.json">自定义配置</option>
                        </select>
                    </div>
                    
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 20px;">
                        <button class="btn btn-primary" onclick="runWithAnalysis()">计算+分析</button>
                        <button class="btn btn-primary" onclick="runVisualization()">生成图表</button>
                        <button class="btn btn-primary" onclick="runTemperatureComparison()">温度对比</button>
                        <button class="btn btn-primary" onclick="runBatchProcessing()">批量处理</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 结果显示面板 -->
        <div class="results-panel">
            <div class="results-header">
                💻 执行命令
            </div>
            <div class="results-content">
                <div class="command-display" id="commandDisplay">
                    <button class="copy-btn" onclick="copyCommand()">复制</button>
                    <div id="commandText">选择文件后将显示执行命令</div>
                </div>
                <div style="text-align: center; margin-top: 20px;">
                    <button class="btn btn-success" onclick="executeCommand()" id="executeBtn" disabled>
                        ▶️ 执行命令
                    </button>
                </div>
            </div>
        </div>

        <!-- 帮助信息 -->
        <div class="help-section">
            <h3>💡 使用说明</h3>
            <p><strong>新手用户：</strong></p>
            <ol>
                <li>选择您的 .bands 文件</li>
                <li>点击"开始完整分析"</li>
                <li>等待计算完成，查看结果文件夹</li>
            </ol>
            <p><strong>结果位置：</strong>所有结果统一保存在 <code>CASTEP_Results</code> 目录中，按材料和时间自动分类。</p>
            <p><strong>专家用户：</strong>切换到专家模式可使用高级功能和自定义配置。</p>
        </div>
    </div>

    <script>
        let selectedFile = null;
        let currentMode = 'beginner';

        // 模式切换
        function switchMode(mode) {
            currentMode = mode;

            // 更新按钮状态
            document.querySelectorAll('.mode-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            // 显示/隐藏对应模式
            if (mode === 'expert') {
                document.getElementById('beginnerMode').style.display = 'none';
                document.getElementById('expertMode').classList.add('active');
            } else {
                document.getElementById('beginnerMode').style.display = 'block';
                document.getElementById('expertMode').classList.remove('active');
            }
        }

        // 文件选择处理
        function handleFileSelect(input) {
            const file = input.files[0];
            if (file) {
                selectedFile = file.name;
                document.getElementById('fileName').textContent = file.name;
                document.getElementById('fileSize').textContent = formatFileSize(file.size);
                document.getElementById('fileInfo').style.display = 'block';

                // 自动设置材料名称
                const materialName = file.name.replace('.bands', '');
                document.getElementById('materialName').value = materialName;

                // 更新命令显示
                updateCommandDisplay();

                // 启用执行按钮
                document.getElementById('executeBtn').disabled = false;
            }
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 获取基本参数
        function getBasicParams() {
            const materialName = document.getElementById('materialName').value || 'material';
            const temperature = document.getElementById('temperature').value || '300';
            const filename = selectedFile || 'file.bands';

            return { materialName, temperature, filename };
        }

        // 更新命令显示
        function updateCommandDisplay() {
            if (!selectedFile) {
                document.getElementById('commandText').textContent = '选择文件后将显示执行命令';
                return;
            }

            const params = getBasicParams();
            const command = `python professional_launcher.py "${params.filename}" --material "${params.materialName}" --temperature ${params.temperature} --complete-analysis`;

            document.getElementById('commandText').textContent = command;
        }

        // 主要功能函数
        function runCompleteAnalysis() {
            if (!selectedFile) {
                alert('请先选择 .bands 文件！');
                return;
            }

            const params = getBasicParams();
            const command = `python professional_launcher.py "${params.filename}" --material "${params.materialName}" --temperature ${params.temperature} --complete-analysis`;

            displayCommand(command);
            showExecutionInfo('完整分析', '将执行材料分析、电导率计算、可视化和报告生成');
        }

        function runBasicCalculation() {
            if (!selectedFile) {
                alert('请先选择 .bands 文件！');
                return;
            }

            const params = getBasicParams();
            const command = `python professional_launcher.py "${params.filename}" --material "${params.materialName}" --temperature ${params.temperature} --basic-only`;

            displayCommand(command);
            showExecutionInfo('基本计算', '仅执行电导率计算，不生成图表');
        }

        function runWithAnalysis() {
            if (!selectedFile) {
                alert('请先选择 .bands 文件！');
                return;
            }

            const params = getBasicParams();
            const config = document.getElementById('configFile').value;
            let command = `python professional_launcher.py "${params.filename}" --material "${params.materialName}" --temperature ${params.temperature} --with-analysis`;

            if (config) {
                command += ` --config ${config}`;
            }

            displayCommand(command);
            showExecutionInfo('计算+分析', '执行计算并进行详细的材料分析');
        }

        function runVisualization() {
            if (!selectedFile) {
                alert('请先选择 .bands 文件！');
                return;
            }

            const params = getBasicParams();
            const command = `python professional_launcher.py "${params.filename}" --material "${params.materialName}" --temperature ${params.temperature} --visualization-only`;

            displayCommand(command);
            showExecutionInfo('生成图表', '生成所有可视化图表和HTML报告');
        }

        function runTemperatureComparison() {
            if (!selectedFile) {
                alert('请先选择 .bands 文件！');
                return;
            }

            const params = getBasicParams();
            const command = `python professional_launcher.py "${params.filename}" --material "${params.materialName}" --temperature-comparison 300,500,800`;

            displayCommand(command);
            showExecutionInfo('温度对比', '在多个温度下进行电导率计算对比');
        }

        function runBatchProcessing() {
            const command = `python professional_launcher.py --batch-mode --input-dir ./ --output-dir CASTEP_Results`;

            displayCommand(command);
            showExecutionInfo('批量处理', '处理当前目录下的所有 .bands 文件');
        }

        // 结果查看功能
        function openResultsFolder() {
            alert('结果文件夹位置：CASTEP_Results/\\n\\n请在文件管理器中打开此目录查看所有计算结果。');
        }

        function viewLatestReport() {
            alert('最新报告位置：CASTEP_Results/calculations/[最新会话]/reports/session_report.html\\n\\n请在浏览器中打开此文件查看详细报告。');
        }

        // 显示命令
        function displayCommand(command) {
            document.getElementById('commandText').textContent = command;
        }

        // 显示执行信息
        function showExecutionInfo(title, description) {
            const info = `执行任务: ${title}
描述: ${description}

结果将保存到: CASTEP_Results/calculations/[材料名_时间戳]/

包含文件:
- input/: 输入文件
- output/: 计算结果
- plots/: 图表文件
- reports/: HTML报告

执行完成后请查看结果文件夹。`;

            setTimeout(() => {
                alert(info);
            }, 100);
        }

        // 复制命令
        function copyCommand() {
            const commandText = document.getElementById('commandText').textContent;

            if (commandText === '选择文件后将显示执行命令') {
                alert('请先选择文件！');
                return;
            }

            navigator.clipboard.writeText(commandText).then(() => {
                const copyBtn = document.querySelector('.copy-btn');
                const originalText = copyBtn.textContent;
                copyBtn.textContent = '已复制!';
                copyBtn.style.background = '#27ae60';

                setTimeout(() => {
                    copyBtn.textContent = originalText;
                    copyBtn.style.background = '#3498db';
                }, 2000);
            }).catch(err => {
                // 备用复制方法
                const textArea = document.createElement('textarea');
                textArea.value = commandText;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);

                alert('命令已复制到剪贴板！');
            });
        }

        // 执行命令（模拟）
        function executeCommand() {
            const commandText = document.getElementById('commandText').textContent;

            if (commandText === '选择文件后将显示执行命令') {
                alert('请先选择文件！');
                return;
            }

            alert(`即将执行命令：\\n${commandText}\\n\\n请在命令行中运行此命令。\\n\\n注意：确保已安装所有依赖包并且 professional_launcher.py 文件存在。`);
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 监听参数变化
            document.getElementById('materialName').addEventListener('input', updateCommandDisplay);
            document.getElementById('temperature').addEventListener('input', updateCommandDisplay);
        });
    </script>
</body>
</html>
