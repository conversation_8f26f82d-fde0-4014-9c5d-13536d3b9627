<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CASTEP 电导率计算工具 - 专业版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.2em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            color: #7f8c8d;
            font-size: 1.1em;
        }

        .workflow {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            overflow: hidden;
        }

        .workflow-header {
            background: #3498db;
            color: white;
            padding: 20px;
            font-size: 1.3em;
            font-weight: 600;
        }

        .workflow-content {
            padding: 30px;
        }

        .step {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
            padding: 20px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            transition: all 0.3s;
        }

        .step:hover {
            border-color: #3498db;
            background: #f8f9fa;
        }

        .step-number {
            background: #3498db;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 20px;
            flex-shrink: 0;
        }

        .step-content {
            flex: 1;
        }

        .step-title {
            font-size: 1.2em;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .step-description {
            color: #7f8c8d;
            margin-bottom: 15px;
        }

        .step-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-1px);
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .input-group {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-bottom: 10px;
        }

        .input-group label {
            min-width: 80px;
            font-weight: 600;
            color: #2c3e50;
        }

        .input-group input, .input-group select {
            padding: 8px 12px;
            border: 2px solid #bdc3c7;
            border-radius: 4px;
            font-size: 14px;
            flex: 1;
        }

        .input-group input:focus, .input-group select:focus {
            outline: none;
            border-color: #3498db;
        }

        .results-panel {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .results-header {
            background: #27ae60;
            color: white;
            padding: 20px;
            font-size: 1.3em;
            font-weight: 600;
        }

        .results-content {
            padding: 30px;
        }

        .command-display {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 15px 0;
            position: relative;
            word-break: break-all;
        }

        .copy-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #3498db;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }

        .copy-btn:hover {
            background: #2980b9;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-ready { background: #27ae60; }
        .status-pending { background: #f39c12; }
        .status-error { background: #e74c3c; }

        .help-section {
            background: #e8f5e8;
            border: 1px solid #27ae60;
            border-radius: 8px;
            padding: 20px;
            margin-top: 30px;
        }

        .help-section h3 {
            color: #27ae60;
            margin-bottom: 15px;
        }

        .file-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
        }

        .mode-selector {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .mode-btn {
            padding: 10px 20px;
            border: 2px solid #3498db;
            background: white;
            color: #3498db;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .mode-btn.active {
            background: #3498db;
            color: white;
        }

        .expert-mode {
            display: none;
        }

        .expert-mode.active {
            display: block;
        }

        .status-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 20px;
            text-align: center;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #ecf0f1;
            border-radius: 10px;
            overflow: hidden;
            margin: 15px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2980b9);
            width: 0%;
            transition: width 0.3s;
        }

        .result-item {
            background: #e8f5e8;
            border: 1px solid #27ae60;
            border-radius: 5px;
            padding: 10px;
            margin: 5px 0;
            text-align: left;
        }

        .calculating {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1>🔬 CASTEP 电导率计算工具</h1>
            <p>专业版 • 简洁高效 • 结果统一管理</p>
        </div>

        <!-- 模式选择 -->
        <div class="mode-selector">
            <button class="mode-btn active" onclick="switchMode('beginner')">新手模式</button>
            <button class="mode-btn" onclick="switchMode('expert')">专家模式</button>
        </div>

        <!-- 新手模式工作流程 -->
        <div id="beginnerMode" class="workflow">
            <div class="workflow-header">
                📋 推荐工作流程 - 三步完成计算
            </div>
            <div class="workflow-content">
                <!-- 步骤1：选择文件 -->
                <div class="step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <div class="step-title">选择计算文件</div>
                        <div class="step-description">选择您的 .bands 文件，设置材料名称</div>
                        <div class="input-group">
                            <label>文件:</label>
                            <input type="file" id="bandsFile" accept=".bands" onchange="handleFileSelect(this)">
                        </div>
                        <div class="input-group">
                            <label>材料名:</label>
                            <input type="text" id="materialName" placeholder="例如: Cu, Si, GaAs" value="Cu">
                        </div>
                        <div class="input-group">
                            <label>温度:</label>
                            <input type="number" id="temperature" value="300" min="1" max="2000">
                            <span>K</span>
                        </div>
                        <div id="fileInfo" class="file-info" style="display: none;">
                            <strong>已选择:</strong> <span id="fileName"></span><br>
                            <strong>大小:</strong> <span id="fileSize"></span>
                        </div>
                    </div>
                </div>

                <!-- 步骤2：一键计算 -->
                <div class="step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <div class="step-title">执行计算</div>
                        <div class="step-description">一键完成材料分析、电导率计算和可视化</div>
                        <div class="step-controls">
                            <button class="btn btn-primary" onclick="runCompleteAnalysis()">
                                <span class="status-indicator status-ready"></span>
                                开始完整分析
                            </button>
                            <button class="btn btn-secondary" onclick="runBasicCalculation()">
                                仅基本计算
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 步骤3：查看结果 -->
                <div class="step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <div class="step-title">查看结果</div>
                        <div class="step-description">所有结果统一保存在 CASTEP_Results 目录中</div>
                        <div class="step-controls">
                            <button class="btn btn-success" onclick="openResultsFolder()">
                                📁 打开结果文件夹
                            </button>
                            <button class="btn btn-primary" onclick="viewLatestReport()">
                                📋 查看最新报告
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 专家模式 -->
        <div id="expertMode" class="expert-mode">
            <div class="workflow">
                <div class="workflow-header">
                    🔧 专家模式 - 高级功能
                </div>
                <div class="workflow-content">
                    <div class="input-group">
                        <label>配置文件:</label>
                        <select id="configFile">
                            <option value="">默认配置</option>
                            <option value="test_config.json">测试配置</option>
                            <option value="custom_config.json">自定义配置</option>
                        </select>
                    </div>
                    
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 20px;">
                        <button class="btn btn-primary" onclick="runWithAnalysis()">计算+分析</button>
                        <button class="btn btn-primary" onclick="runVisualization()">生成图表</button>
                        <button class="btn btn-primary" onclick="runTemperatureComparison()">温度对比</button>
                        <button class="btn btn-primary" onclick="runBatchProcessing()">批量处理</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 计算状态面板 -->
        <div class="results-panel">
            <div class="results-header">
                📊 计算状态
            </div>
            <div class="results-content">
                <div id="statusDisplay" class="status-display">
                    <div id="statusMessage">选择文件后可开始计算</div>
                    <div id="progressContainer" style="display: none;">
                        <div class="progress-bar">
                            <div class="progress-fill" id="progressFill"></div>
                        </div>
                        <div id="progressText">0%</div>
                    </div>
                </div>
                <div id="resultsContainer" style="display: none; margin-top: 20px;">
                    <h4>📋 计算结果</h4>
                    <div id="resultsList"></div>
                    <div style="text-align: center; margin-top: 15px;">
                        <button class="btn btn-success" onclick="openResultsFolder()" id="openResultsBtn">
                            📁 打开结果文件夹
                        </button>
                        <button class="btn btn-primary" onclick="viewReport()" id="viewReportBtn">
                            📋 查看分析报告
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 帮助信息 -->
        <div class="help-section">
            <h3>💡 使用说明</h3>
            <p><strong>新手用户：</strong></p>
            <ol>
                <li>选择您的 .bands 文件</li>
                <li>点击"开始完整分析"</li>
                <li>等待计算完成，查看结果文件夹</li>
            </ol>
            <p><strong>结果位置：</strong>所有结果统一保存在 <code>CASTEP_Results</code> 目录中，按材料和时间自动分类。</p>
            <p><strong>专家用户：</strong>切换到专家模式可使用高级功能和自定义配置。</p>
        </div>
    </div>

    <script>
        let selectedFile = null;
        let currentMode = 'beginner';
        let currentTaskId = null;
        let statusCheckInterval = null;

        // API基础URL
        const API_BASE = window.location.origin;

        // 模式切换
        function switchMode(mode) {
            currentMode = mode;

            // 更新按钮状态
            document.querySelectorAll('.mode-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            // 显示/隐藏对应模式
            if (mode === 'expert') {
                document.getElementById('beginnerMode').style.display = 'none';
                document.getElementById('expertMode').classList.add('active');
            } else {
                document.getElementById('beginnerMode').style.display = 'block';
                document.getElementById('expertMode').classList.remove('active');
            }
        }

        // 文件选择处理
        function handleFileSelect(input) {
            const file = input.files[0];
            if (file) {
                selectedFile = file;
                document.getElementById('fileName').textContent = file.name;
                document.getElementById('fileSize').textContent = formatFileSize(file.size);
                document.getElementById('fileInfo').style.display = 'block';

                // 自动设置材料名称
                const materialName = file.name.replace('.bands', '');
                document.getElementById('materialName').value = materialName;

                // 更新状态显示
                updateStatusDisplay('文件已选择，可以开始计算');

                // 重置结果显示
                document.getElementById('resultsContainer').style.display = 'none';
            }
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 获取基本参数
        function getBasicParams() {
            const materialName = document.getElementById('materialName').value || 'material';
            const temperature = document.getElementById('temperature').value || '300';

            return { materialName, temperature };
        }

        // 更新状态显示
        function updateStatusDisplay(message) {
            document.getElementById('statusMessage').textContent = message;
        }

        // 启动计算任务
        async function startCalculation(taskType) {
            if (!selectedFile) {
                alert('请先选择 .bands 文件！');
                return;
            }

            const params = getBasicParams();

            // 准备计算参数
            const calculationData = {
                task_type: taskType,
                filename: selectedFile.name,
                material_name: params.materialName,
                temperature: params.temperature
            };

            try {
                // 显示进度
                showProgress();
                updateStatusDisplay('正在启动计算...');
                updateProgress(5, '初始化中...');

                // 发送计算请求
                const response = await fetch(`${API_BASE}/calculate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(calculationData)
                });

                const result = await response.json();

                if (result.success) {
                    currentTaskId = result.task_id;
                    updateStatusDisplay('计算已启动，正在处理...');
                    updateProgress(10, '计算进行中...');

                    // 开始轮询状态
                    startStatusPolling();
                } else {
                    throw new Error(result.error || '启动计算失败');
                }

            } catch (error) {
                hideProgress();
                updateStatusDisplay(`计算启动失败: ${error.message}`);
                alert(`计算启动失败: ${error.message}`);
            }
        }

        // 开始状态轮询
        function startStatusPolling() {
            if (statusCheckInterval) {
                clearInterval(statusCheckInterval);
            }

            statusCheckInterval = setInterval(async () => {
                try {
                    const response = await fetch(`${API_BASE}/status?task_id=${currentTaskId}`);
                    const result = await response.json();

                    if (result.success) {
                        updateProgress(result.progress, result.message);

                        if (result.status === 'completed') {
                            clearInterval(statusCheckInterval);
                            handleCalculationComplete(result.result);
                        } else if (result.status === 'error') {
                            clearInterval(statusCheckInterval);
                            handleCalculationError(result.error);
                        }
                    }
                } catch (error) {
                    console.error('状态查询失败:', error);
                }
            }, 1000); // 每秒查询一次
        }

        // 处理计算完成
        function handleCalculationComplete(result) {
            hideProgress();
            updateStatusDisplay('计算完成！');

            // 显示结果
            displayResults(result);
        }

        // 处理计算错误
        function handleCalculationError(error) {
            hideProgress();
            updateStatusDisplay(`计算失败: ${error}`);
            alert(`计算失败: ${error}`);
        }

        // 显示进度
        function showProgress() {
            document.getElementById('progressContainer').style.display = 'block';
            document.getElementById('statusDisplay').classList.add('calculating');
        }

        // 隐藏进度
        function hideProgress() {
            document.getElementById('progressContainer').style.display = 'none';
            document.getElementById('statusDisplay').classList.remove('calculating');
        }

        // 更新进度
        function updateProgress(percent, message) {
            document.getElementById('progressFill').style.width = percent + '%';
            document.getElementById('progressText').textContent = `${percent}% - ${message}`;
        }

        // 显示计算结果
        function displayResults(result) {
            const resultsContainer = document.getElementById('resultsContainer');
            const resultsList = document.getElementById('resultsList');

            // 清空之前的结果
            resultsList.innerHTML = '';

            // 显示会话信息
            const sessionInfo = document.createElement('div');
            sessionInfo.className = 'result-item';
            sessionInfo.innerHTML = `
                <strong>计算会话:</strong> ${result.session_id}<br>
                <strong>材料:</strong> ${result.session_info.material_name}<br>
                <strong>状态:</strong> ${result.session_info.status}
            `;
            resultsList.appendChild(sessionInfo);

            // 显示生成的文件
            if (result.result_paths.report_html) {
                const reportItem = document.createElement('div');
                reportItem.className = 'result-item';
                reportItem.innerHTML = `
                    <strong>📋 分析报告:</strong>
                    <a href="${API_BASE}/files/${result.result_paths.report_html}" target="_blank">查看HTML报告</a>
                `;
                resultsList.appendChild(reportItem);
            }

            if (result.result_paths.plots.length > 0) {
                const plotsItem = document.createElement('div');
                plotsItem.className = 'result-item';
                plotsItem.innerHTML = `<strong>📊 生成的图表:</strong><br>`;
                result.result_paths.plots.forEach(plot => {
                    plotsItem.innerHTML += `<a href="${API_BASE}/files/${plot}" target="_blank">${plot.split('/').pop()}</a><br>`;
                });
                resultsList.appendChild(plotsItem);
            }

            // 保存结果路径供后续使用
            window.lastResult = result;

            // 显示结果容器
            resultsContainer.style.display = 'block';
        }

        // 主要功能函数
        function runCompleteAnalysis() {
            startCalculation('complete');
        }

        function runBasicCalculation() {
            startCalculation('basic');
        }

        function runWithAnalysis() {
            startCalculation('complete');
        }

        function runVisualization() {
            startCalculation('visualization');
        }

        function runTemperatureComparison() {
            alert('温度对比功能正在开发中...');
        }

        function runBatchProcessing() {
            alert('批量处理功能正在开发中...');
        }

        // 结果查看功能
        function openResultsFolder() {
            if (window.lastResult) {
                const folderPath = window.lastResult.result_paths.session_dir;
                alert(`结果文件夹位置：${folderPath}\\n\\n请在文件管理器中打开此目录查看所有计算结果。`);
            } else {
                alert('结果文件夹位置：CASTEP_Results/\\n\\n请在文件管理器中打开此目录查看所有计算结果。');
            }
        }

        function viewReport() {
            if (window.lastResult && window.lastResult.result_paths.report_html) {
                const reportUrl = `${API_BASE}/files/${window.lastResult.result_paths.report_html}`;
                window.open(reportUrl, '_blank');
            } else {
                alert('暂无可查看的报告，请先完成计算。');
            }
        }

        function viewLatestReport() {
            viewReport();
        }



        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateStatusDisplay('请选择 .bands 文件开始计算');

            // 检查服务器连接
            checkServerConnection();
        });

        // 检查服务器连接
        async function checkServerConnection() {
            try {
                const response = await fetch(`${API_BASE}/`);
                if (response.ok) {
                    console.log('服务器连接正常');
                } else {
                    updateStatusDisplay('服务器连接异常，请检查API服务器是否启动');
                }
            } catch (error) {
                updateStatusDisplay('无法连接到服务器，请启动API服务器：python api_server.py');
            }
        }
    </script>
</body>
</html>
