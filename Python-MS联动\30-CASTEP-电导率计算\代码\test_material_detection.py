#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
材料类型检测测试脚本

测试材料分析器对不同类型材料的识别能力

作者：Claude AI助手
日期：2025-01-20
"""

import numpy as np
import json
from material_analyzer import MaterialAnalyzer, MaterialType

def create_test_bands(material_type, n_bands=10, n_kpoints=100):
    """创建测试用的能带数据"""

    if material_type == "metal":
        # 金属：费米能级穿过能带
        bands = np.random.random((n_bands, n_kpoints)) * 10 - 2  # -2 到 8 eV
        fermi_energy = 3.0  # 费米能级在能带范围内
        n_electrons = 50.0  # 部分填充

    elif material_type == "semiconductor":
        # 半导体：有适中的带隙
        # 前5个能带为价带：-2 到 0 eV
        valence_bands = np.random.random((5, n_kpoints)) * 2 - 2
        # 后5个能带为导带：2 到 6 eV (带隙约2 eV)
        conduction_bands = np.random.random((5, n_kpoints)) * 4 + 2
        bands = np.vstack([valence_bands, conduction_bands])
        fermi_energy = 1.0  # 费米能级在带隙中
        n_electrons = 10.0  # 刚好填满价带 (5个能带 × 2个电子/能带)

    elif material_type == "insulator":
        # 绝缘体：大带隙
        # 前5个能带为价带：-5 到-1 eV
        valence_bands = np.random.random((5, n_kpoints)) * 4 - 5
        # 后5个能带为导带：5 到 10 eV (带隙约6 eV)
        conduction_bands = np.random.random((5, n_kpoints)) * 5 + 5
        bands = np.vstack([valence_bands, conduction_bands])
        fermi_energy = 2.0  # 费米能级在大带隙中
        n_electrons = 10.0  # 刚好填满价带

    else:
        raise ValueError(f"未知的材料类型: {material_type}")

    return bands, fermi_energy, n_electrons

def test_material_detection():
    """测试材料类型检测功能"""
    
    # 加载配置
    with open('config.json', 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    analyzer = MaterialAnalyzer(config)
    
    print("="*60)
    print("材料类型检测测试")
    print("="*60)
    
    test_materials = ["metal", "semiconductor", "insulator"]
    
    for material_type in test_materials:
        print(f"\n测试 {material_type.upper()} 材料:")
        print("-" * 40)
        
        # 创建测试数据
        bands, fermi_energy, n_electrons = create_test_bands(material_type)
        
        # 进行材料分析
        detected_type, analysis = analyzer.analyze_material(
            bands, fermi_energy, n_electrons, n_spins=1
        )
        
        # 输出结果
        print(f"预期类型: {material_type}")
        print(f"检测类型: {detected_type.value}")
        print(f"检测正确: {'✓' if detected_type.value == material_type else '✗'}")
        print(f"费米能级: {analysis['fermi_energy']:.3f} eV")
        print(f"估算带隙: {analysis['band_gap']:.3f} eV")
        print(f"费米穿过能带: {'是' if analysis['is_fermi_crossing'] else '否'}")
        print(f"穿过的能带数: {analysis['bands_at_fermi']}")
        
        # 显示推荐参数
        props = analysis['material_properties']
        print(f"推荐电导率: {props['typical_conductivity']:.2e} S/m/s")
        print(f"推荐弛豫时间: {props['relaxation_time']:.2e} s")

def test_edge_cases():
    """测试边界情况"""
    
    print("\n" + "="*60)
    print("边界情况测试")
    print("="*60)
    
    # 加载配置
    with open('config.json', 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    analyzer = MaterialAnalyzer(config)
    
    # 测试1：费米能级刚好在能带边缘
    print("\n测试1: 费米能级在能带边缘")
    bands = np.array([[1, 2, 3], [4, 5, 6], [7, 8, 9]])  # 3个能带，3个k点
    fermi_energy = 3.0  # 刚好在第一个能带的最大值
    detected_type, analysis = analyzer.analyze_material(bands, fermi_energy, 6.0, 1)
    print(f"检测类型: {detected_type.value}")
    print(f"费米穿过能带: {'是' if analysis['is_fermi_crossing'] else '否'}")
    
    # 测试2：零带隙半金属
    print("\n测试2: 零带隙半金属")
    bands = np.array([[0, 1, 2], [2, 3, 4]])  # 能带刚好接触
    fermi_energy = 2.0
    detected_type, analysis = analyzer.analyze_material(bands, fermi_energy, 6.0, 1)
    print(f"检测类型: {detected_type.value}")
    print(f"估算带隙: {analysis['band_gap']:.3f} eV")

if __name__ == "__main__":
    test_material_detection()
    test_edge_cases()
    print("\n测试完成！")
