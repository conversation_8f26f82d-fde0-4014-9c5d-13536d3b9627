# CASTEP 电导率计算工具 Web 界面使用指南

## 概述

为了让用户更方便地使用CASTEP电导率计算工具，我们提供了两种Web界面：

1. **简化界面** (`simple_interface.html`) - 命令生成器，无需服务器
2. **完整界面** (`web_interface.html` + `web_server.py`) - 功能完整的Web应用

## 🚀 快速开始

### 方法一：使用简化界面（推荐）

1. **直接打开HTML文件**
   ```bash
   # 在浏览器中打开
   start simple_interface.html  # Windows
   open simple_interface.html   # Mac
   xdg-open simple_interface.html  # Linux
   ```

2. **设置参数并生成命令**
   - 输入文件名（如 `Cu.bands`）
   - 设置温度、输出目录等参数
   - 点击功能按钮生成对应命令
   - 复制命令到命令行执行

### 方法二：使用完整Web界面

1. **启动Web服务器**
   ```bash
   python start_web.py
   ```
   或者
   ```bash
   python web_server.py -p 8080
   ```

2. **在浏览器中访问**
   - 自动打开浏览器访问 `http://localhost:8080`
   - 或手动在浏览器中输入地址

## 📋 界面功能说明

### 简化界面功能

#### 基本设置
- **文件名**：输入要处理的 .bands 文件名
- **温度**：设置计算温度（K）
- **输出目录**：指定结果保存目录
- **配置文件**：选择使用的配置文件

#### 功能按钮

**🧮 基本计算**
- `基本计算`：仅计算电导率
- `计算+分析`：计算 + 材料分析
- `计算+保存`：计算 + 保存结果到文件
- `自定义温度`：使用指定温度计算

**📊 可视化功能**
- `能带结构图`：生成能带结构可视化
- `电导率张量图`：生成电导率张量图
- `材料分析图`：生成材料分析图表
- `所有图表`：生成所有可视化图表
- `HTML报告`：生成完整HTML报告

**🚀 高级功能**
- `完整分析`：执行完整的分析流程
- `一键开始`：推荐的快速开始选项
- `温度对比`：生成多温度对比命令
- `批量处理`：生成批量处理脚本

### 完整界面功能

完整界面包含简化界面的所有功能，另外还有：

- **文件上传**：直接在界面中选择文件
- **实时执行**：点击按钮直接执行计算
- **进度显示**：显示计算进度和状态
- **结果查看**：直接在界面中查看结果
- **日志输出**：实时显示执行日志

## 💡 使用技巧

### 1. 推荐工作流程

**对于新用户：**
```
1. 使用简化界面生成"一键开始"命令
2. 复制命令到命令行执行
3. 查看生成的HTML报告
4. 根据需要调整参数重新计算
```

**对于高级用户：**
```
1. 使用完整界面进行实时计算
2. 利用批量处理功能处理多个文件
3. 进行温度对比分析
4. 自定义配置文件优化计算
```

### 2. 常用命令组合

**快速分析：**
```bash
python launcher.py Cu.bands --show-analysis --report --save-plots results
```

**完整可视化：**
```bash
python launcher.py Cu.bands --plot --save-plots visualization
```

**温度对比：**
```bash
python launcher.py Cu.bands -t 300 --plot-conductivity --save-plots temp_300K
python launcher.py Cu.bands -t 500 --plot-conductivity --save-plots temp_500K
python launcher.py Cu.bands -t 800 --plot-conductivity --save-plots temp_800K
```

### 3. 批量处理

**Windows PowerShell：**
```powershell
Get-ChildItem *.bands | ForEach-Object {
    $name = $_.BaseName
    python launcher.py $_.Name --report --save-plots "results/$name"
}
```

**Linux/Mac Bash：**
```bash
for file in *.bands; do
    name=$(basename "$file" .bands)
    python launcher.py "$file" --report --save-plots "results/$name"
done
```

## 🔧 自定义配置

### 创建自定义配置文件

1. 复制 `config.json` 为 `custom_config.json`
2. 修改其中的参数
3. 在界面中选择自定义配置

### 常用配置调整

**提高计算精度：**
```json
{
  "calculation_parameters": {
    "delta_k": 0.005,
    "min_derivative_threshold": 1e-15,
    "interpolation_neighbors": 12
  }
}
```

**调整材料检测参数：**
```json
{
  "material_detection": {
    "fermi_level_tolerance": 0.005,
    "band_gap_threshold": 0.05
  }
}
```

## 📊 结果文件说明

### 输出目录结构
```
results/
├── band_structure.png          # 能带结构图
├── conductivity_tensor.png     # 电导率张量图
├── material_analysis.png       # 材料分析图
├── report.html                 # 完整HTML报告
└── results.txt                 # 文本结果文件
```

### HTML报告内容
- 材料基本信息表格
- 材料分析结果和图表
- 能带结构可视化
- 电导率计算结果
- 详细的数值表格

## ⚠️ 注意事项

### 1. 文件路径
- 确保 .bands 文件在当前工作目录
- 使用相对路径或绝对路径
- 避免文件名包含特殊字符

### 2. 内存使用
- 大型系统可能需要较多内存
- 可选择性生成特定图表
- 关闭不必要的可视化选项

### 3. 浏览器兼容性
- 推荐使用现代浏览器（Chrome、Firefox、Edge）
- 确保启用JavaScript
- 某些功能需要支持ES6

### 4. 服务器模式
- 完整界面需要Python服务器
- 确保端口未被占用
- 防火墙可能需要允许本地连接

## 🐛 故障排除

### 常见问题

**1. 界面无法打开**
- 检查文件路径是否正确
- 确保浏览器支持本地文件访问
- 尝试使用服务器模式

**2. 命令执行失败**
- 检查Python环境和依赖包
- 确认文件名和路径正确
- 查看错误信息并对应解决

**3. 图表显示异常**
- 检查matplotlib和seaborn安装
- 确认字体文件存在
- 尝试更新图形库版本

**4. 服务器启动失败**
- 检查端口是否被占用
- 确认Python版本兼容性
- 查看详细错误信息

### 获取帮助

1. 查看 `README.md` 了解基本使用
2. 阅读 `VISUALIZATION_GUIDE.md` 了解可视化功能
3. 检查 `IMPROVEMENTS_SUMMARY.md` 了解最新改进
4. 使用命令行 `python launcher.py --help` 查看所有选项

## 🎯 最佳实践

1. **首次使用**：从简化界面的"一键开始"功能开始
2. **参数调整**：根据材料特性调整温度和配置
3. **结果验证**：对比不同参数下的计算结果
4. **文档记录**：保存HTML报告作为分析记录
5. **批量处理**：对多个材料使用批量处理功能

通过Web界面，您可以更直观、便捷地使用CASTEP电导率计算工具的所有功能！
