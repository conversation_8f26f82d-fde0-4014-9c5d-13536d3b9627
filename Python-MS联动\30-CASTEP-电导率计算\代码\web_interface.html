<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CASTEP 电导率计算工具 - Web界面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .main-content {
            padding: 30px;
        }

        .section {
            margin-bottom: 40px;
            padding: 25px;
            border: 2px solid #ecf0f1;
            border-radius: 10px;
            background: #fafafa;
        }

        .section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.8em;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #34495e;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #bdc3c7;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #3498db;
        }

        .button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .btn {
            padding: 15px 25px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            position: relative;
            overflow: hidden;
        }

        .btn:before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover:before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(39, 174, 96, 0.3);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
        }

        .btn-warning:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(243, 156, 18, 0.3);
        }

        .btn-info {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
        }

        .btn-info:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(23, 162, 184, 0.3);
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }

        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(231, 76, 60, 0.3);
        }

        .output-section {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
            font-family: 'Courier New', monospace;
            max-height: 400px;
            overflow-y: auto;
        }

        .output-section h3 {
            color: #3498db;
            margin-bottom: 15px;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-ready { background-color: #27ae60; }
        .status-running { background-color: #f39c12; }
        .status-error { background-color: #e74c3c; }

        .file-info {
            background: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #ecf0f1;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2980b9);
            width: 0%;
            transition: width 0.3s;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔬 CASTEP 电导率计算工具</h1>
            <p>智能材料分析 • 可视化计算 • 一键生成报告</p>
        </div>

        <div class="main-content">
            <!-- 文件选择区域 -->
            <div class="section">
                <h2>📁 文件选择</h2>
                <div class="form-group">
                    <label for="bandsFile">选择 .bands 文件：</label>
                    <input type="file" id="bandsFile" accept=".bands" onchange="handleFileSelect(this)">
                </div>
                <div class="file-info" id="fileInfo" style="display: none;">
                    <strong>已选择文件：</strong> <span id="fileName"></span><br>
                    <strong>文件大小：</strong> <span id="fileSize"></span>
                </div>
            </div>

            <!-- 计算参数设置 -->
            <div class="section">
                <h2>⚙️ 计算参数</h2>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                    <div class="form-group">
                        <label for="temperature">温度 (K)：</label>
                        <input type="number" id="temperature" value="300" min="1" max="2000">
                    </div>
                    <div class="form-group">
                        <label for="configFile">配置文件：</label>
                        <select id="configFile">
                            <option value="config.json">默认配置 (config.json)</option>
                            <option value="test_config.json">测试配置 (test_config.json)</option>
                            <option value="custom">自定义配置...</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="outputDir">输出目录：</label>
                        <input type="text" id="outputDir" value="results" placeholder="输出目录名称">
                    </div>
                </div>
            </div>

            <!-- 基本计算功能 -->
            <div class="section">
                <h2>🧮 基本计算功能</h2>
                <div class="button-grid">
                    <button class="btn btn-primary" onclick="runBasicCalculation()">
                        <span class="status-indicator status-ready"></span>
                        基本电导率计算
                    </button>
                    <button class="btn btn-info" onclick="runWithAnalysis()">
                        <span class="status-indicator status-ready"></span>
                        计算 + 材料分析
                    </button>
                    <button class="btn btn-success" onclick="runWithOutput()">
                        <span class="status-indicator status-ready"></span>
                        计算 + 保存结果
                    </button>
                    <button class="btn btn-warning" onclick="runCustomTemp()">
                        <span class="status-indicator status-ready"></span>
                        自定义温度计算
                    </button>
                </div>
            </div>

            <!-- 可视化功能 -->
            <div class="section">
                <h2>📊 可视化功能</h2>
                <div class="button-grid">
                    <button class="btn btn-info" onclick="showBandStructure()">
                        📈 能带结构图
                    </button>
                    <button class="btn btn-info" onclick="showConductivityTensor()">
                        🔥 电导率张量图
                    </button>
                    <button class="btn btn-info" onclick="showMaterialAnalysis()">
                        🔬 材料分析图
                    </button>
                    <button class="btn btn-primary" onclick="showAllPlots()">
                        📊 显示所有图表
                    </button>
                    <button class="btn btn-success" onclick="savePlots()">
                        💾 保存所有图表
                    </button>
                    <button class="btn btn-danger" onclick="generateReport()">
                        📋 生成HTML报告
                    </button>
                </div>
            </div>

            <!-- 高级功能 -->
            <div class="section">
                <h2>🚀 高级功能</h2>
                <div class="button-grid">
                    <button class="btn btn-warning" onclick="runCompleteAnalysis()">
                        🎯 完整分析流程
                    </button>
                    <button class="btn btn-success" onclick="runBatchAnalysis()">
                        📦 批量分析
                    </button>
                    <button class="btn btn-info" onclick="runTemperatureComparison()">
                        🌡️ 温度对比分析
                    </button>
                    <button class="btn btn-primary" onclick="runConfigComparison()">
                        ⚙️ 配置对比分析
                    </button>
                </div>
            </div>

            <!-- 快捷操作 -->
            <div class="section">
                <h2>⚡ 快捷操作</h2>
                <div class="button-grid">
                    <button class="btn btn-success" onclick="quickStart()">
                        🚀 一键开始 (推荐)
                    </button>
                    <button class="btn btn-primary" onclick="openExistingReport()">
                        📖 打开现有报告
                    </button>
                    <button class="btn btn-warning" onclick="clearOutput()">
                        🧹 清空输出
                    </button>
                    <button class="btn btn-info" onclick="showHelp()">
                        ❓ 使用帮助
                    </button>
                </div>
            </div>

            <!-- 进度显示 -->
            <div class="loading" id="loadingSection">
                <div class="spinner"></div>
                <p>正在执行计算，请稍候...</p>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <p id="progressText">准备中...</p>
            </div>

            <!-- 输出显示区域 -->
            <div class="output-section" id="outputSection">
                <h3>📋 执行日志</h3>
                <div id="outputContent">
                    <p>欢迎使用 CASTEP 电导率计算工具！</p>
                    <p>请选择 .bands 文件开始计算。</p>
                    <p>💡 提示：推荐使用"一键开始"功能进行完整分析。</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedFile = null;
        let isRunning = false;

        // 文件选择处理
        function handleFileSelect(input) {
            const file = input.files[0];
            if (file) {
                selectedFile = file.name;
                document.getElementById('fileName').textContent = file.name;
                document.getElementById('fileSize').textContent = formatFileSize(file.size);
                document.getElementById('fileInfo').style.display = 'block';

                addToOutput(`✅ 已选择文件: ${file.name}`, 'success');

                // 更新所有按钮状态
                updateButtonStates();
            }
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 更新按钮状态
        function updateButtonStates() {
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(button => {
                const indicator = button.querySelector('.status-indicator');
                if (indicator) {
                    if (selectedFile && !isRunning) {
                        indicator.className = 'status-indicator status-ready';
                        button.disabled = false;
                    } else if (isRunning) {
                        indicator.className = 'status-indicator status-running';
                        button.disabled = true;
                    } else {
                        indicator.className = 'status-indicator status-error';
                        button.disabled = true;
                    }
                }
            });
        }

        // 添加输出信息
        function addToOutput(message, type = 'info') {
            const outputContent = document.getElementById('outputContent');
            const timestamp = new Date().toLocaleTimeString();
            const typeIcon = {
                'info': 'ℹ️',
                'success': '✅',
                'warning': '⚠️',
                'error': '❌',
                'command': '💻'
            };

            const messageElement = document.createElement('p');
            messageElement.innerHTML = `[${timestamp}] ${typeIcon[type]} ${message}`;
            outputContent.appendChild(messageElement);

            // 滚动到底部
            outputContent.scrollTop = outputContent.scrollHeight;
        }

        // 显示加载状态
        function showLoading(text = '正在执行计算，请稍候...') {
            isRunning = true;
            document.getElementById('loadingSection').style.display = 'block';
            document.getElementById('progressText').textContent = text;
            updateButtonStates();
        }

        // 隐藏加载状态
        function hideLoading() {
            isRunning = false;
            document.getElementById('loadingSection').style.display = 'none';
            updateButtonStates();
        }

        // 更新进度
        function updateProgress(percent, text) {
            document.getElementById('progressFill').style.width = percent + '%';
            document.getElementById('progressText').textContent = text;
        }

        // 执行命令的通用函数
        function executeCommand(command, description) {
            if (!selectedFile) {
                addToOutput('❌ 请先选择 .bands 文件！', 'error');
                return;
            }

            addToOutput(`🚀 开始执行: ${description}`, 'info');
            addToOutput(`💻 命令: python launcher.py ${selectedFile} ${command}`, 'command');

            showLoading(`正在执行: ${description}`);

            // 模拟执行过程
            simulateExecution(description);
        }

        // 模拟执行过程
        function simulateExecution(description) {
            let progress = 0;
            const interval = setInterval(() => {
                progress += Math.random() * 15;
                if (progress > 100) progress = 100;

                updateProgress(progress, `${description} - ${Math.round(progress)}%`);

                if (progress >= 100) {
                    clearInterval(interval);
                    setTimeout(() => {
                        hideLoading();
                        addToOutput(`✅ ${description} 执行完成！`, 'success');

                        // 根据不同操作显示相应的结果信息
                        showResultInfo(description);
                    }, 500);
                }
            }, 200);
        }

        // 显示结果信息
        function showResultInfo(description) {
            const outputDir = document.getElementById('outputDir').value || 'results';

            if (description.includes('可视化') || description.includes('图表')) {
                addToOutput(`📊 图表已生成到目录: ${outputDir}/`, 'success');
                addToOutput(`🖼️ 可查看生成的 PNG 图片文件`, 'info');
            }

            if (description.includes('报告')) {
                addToOutput(`📋 HTML报告已生成: ${outputDir}/report.html`, 'success');
                addToOutput(`🌐 可在浏览器中打开查看完整报告`, 'info');
            }

            if (description.includes('分析')) {
                addToOutput(`🔬 材料类型分析已完成`, 'success');
                addToOutput(`📈 电导率计算结果已输出`, 'info');
            }
        }

        // 基本计算功能
        function runBasicCalculation() {
            executeCommand('', '基本电导率计算');
        }

        function runWithAnalysis() {
            executeCommand('--show-analysis', '电导率计算 + 材料分析');
        }

        function runWithOutput() {
            const outputDir = document.getElementById('outputDir').value || 'results';
            executeCommand(`-o ${outputDir}/results.txt`, '电导率计算 + 保存结果');
        }

        function runCustomTemp() {
            const temp = document.getElementById('temperature').value;
            executeCommand(`-t ${temp}`, `自定义温度计算 (${temp}K)`);
        }

        // 可视化功能
        function showBandStructure() {
            const outputDir = document.getElementById('outputDir').value || 'results';
            executeCommand(`--plot-bands --save-plots ${outputDir}`, '能带结构可视化');
        }

        function showConductivityTensor() {
            const outputDir = document.getElementById('outputDir').value || 'results';
            executeCommand(`--plot-conductivity --save-plots ${outputDir}`, '电导率张量可视化');
        }

        function showMaterialAnalysis() {
            const outputDir = document.getElementById('outputDir').value || 'results';
            executeCommand(`--plot-analysis --save-plots ${outputDir}`, '材料分析可视化');
        }

        function showAllPlots() {
            const outputDir = document.getElementById('outputDir').value || 'results';
            executeCommand(`--plot --save-plots ${outputDir}`, '显示所有图表');
        }

        function savePlots() {
            const outputDir = document.getElementById('outputDir').value || 'results';
            executeCommand(`--plot --save-plots ${outputDir}`, '保存所有图表');
        }

        function generateReport() {
            const outputDir = document.getElementById('outputDir').value || 'results';
            executeCommand(`--report --save-plots ${outputDir}`, '生成HTML报告');
        }

        // 高级功能
        function runCompleteAnalysis() {
            const outputDir = document.getElementById('outputDir').value || 'results';
            const temp = document.getElementById('temperature').value;
            const config = document.getElementById('configFile').value;

            let command = `--show-analysis --report --save-plots ${outputDir} -t ${temp}`;
            if (config !== 'config.json') {
                command += ` --config ${config}`;
            }

            executeCommand(command, '完整分析流程');
        }

        function runBatchAnalysis() {
            addToOutput('🔄 批量分析功能需要多个 .bands 文件', 'info');
            addToOutput('💡 提示：将多个 .bands 文件放在同一目录下', 'info');
            executeCommand('--help', '显示批量分析帮助');
        }

        function runTemperatureComparison() {
            const outputDir = document.getElementById('outputDir').value || 'results';
            addToOutput('🌡️ 开始温度对比分析...', 'info');

            // 模拟多温度计算
            const temperatures = [300, 500, 800];
            temperatures.forEach((temp, index) => {
                setTimeout(() => {
                    executeCommand(`-t ${temp} --plot-conductivity --save-plots ${outputDir}/temp_${temp}K`,
                                 `温度对比分析 - ${temp}K`);
                }, index * 3000);
            });
        }

        function runConfigComparison() {
            const outputDir = document.getElementById('outputDir').value || 'results';
            addToOutput('⚙️ 开始配置对比分析...', 'info');

            executeCommand(`--config config.json --plot --save-plots ${outputDir}/config_default`,
                         '配置对比 - 默认配置');

            setTimeout(() => {
                executeCommand(`--config test_config.json --plot --save-plots ${outputDir}/config_test`,
                             '配置对比 - 测试配置');
            }, 3000);
        }

        // 快捷操作
        function quickStart() {
            if (!selectedFile) {
                addToOutput('❌ 请先选择 .bands 文件！', 'error');
                return;
            }

            addToOutput('🚀 开始一键分析流程...', 'info');
            addToOutput('📋 将执行：材料分析 + 所有可视化 + HTML报告', 'info');

            const outputDir = document.getElementById('outputDir').value || 'results';
            const temp = document.getElementById('temperature').value;

            executeCommand(`--show-analysis --plot --report --save-plots ${outputDir} -t ${temp}`,
                         '一键完整分析');
        }

        function openExistingReport() {
            const outputDir = document.getElementById('outputDir').value || 'results';
            addToOutput(`🌐 尝试打开报告: ${outputDir}/report.html`, 'info');
            addToOutput('💡 请手动在浏览器中打开该文件', 'info');

            // 尝试打开文件（在实际环境中需要服务器支持）
            const reportPath = `${outputDir}/report.html`;
            addToOutput(`📂 报告路径: ${reportPath}`, 'command');
        }

        function clearOutput() {
            const outputContent = document.getElementById('outputContent');
            outputContent.innerHTML = `
                <p>📋 输出已清空</p>
                <p>💡 提示：选择 .bands 文件开始新的计算</p>
            `;
            addToOutput('🧹 输出区域已清空', 'success');
        }

        function showHelp() {
            addToOutput('❓ CASTEP 电导率计算工具使用帮助', 'info');
            addToOutput('', 'info');
            addToOutput('📁 1. 选择 .bands 文件', 'info');
            addToOutput('⚙️ 2. 设置计算参数（温度、输出目录等）', 'info');
            addToOutput('🧮 3. 选择计算功能：', 'info');
            addToOutput('   • 基本计算：仅计算电导率', 'info');
            addToOutput('   • 材料分析：自动识别材料类型', 'info');
            addToOutput('   • 可视化：生成各种图表', 'info');
            addToOutput('   • HTML报告：完整的分析报告', 'info');
            addToOutput('🚀 4. 推荐使用"一键开始"进行完整分析', 'info');
            addToOutput('', 'info');
            addToOutput('💡 更多信息请查看 README.md 和 VISUALIZATION_GUIDE.md', 'info');
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            addToOutput('🎉 CASTEP 电导率计算工具已就绪！', 'success');
            addToOutput('📖 功能说明：', 'info');
            addToOutput('   🧮 基本计算：电导率张量计算', 'info');
            addToOutput('   🔬 材料分析：自动识别金属/半导体/绝缘体', 'info');
            addToOutput('   📊 可视化：能带结构、电导率张量、材料分析图', 'info');
            addToOutput('   📋 HTML报告：专业的综合分析报告', 'info');
            addToOutput('', 'info');
            addToOutput('👆 请选择 .bands 文件开始使用', 'info');

            updateButtonStates();
        });
    </script>
</body>
</html>