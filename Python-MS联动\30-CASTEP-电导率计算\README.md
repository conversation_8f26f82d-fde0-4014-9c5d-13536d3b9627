# CASTEP 电导率计算项目

## 项目概述

本项目基于Materials Studio的CASTEP模块计算结果，开发了一套专业电导率计算与可视化工具。项目遵循材料科学领域的标准计算流程，实现了两种电导率计算方法：基于介电函数的直接计算和基于Boltzmann输运理论的温度依赖计算。

## 项目结构

```
30-CASTEP-电导率计算/
├── 计算步骤.md        # CASTEP电导率计算的理论与步骤详解
├── 代码/              # Python程序代码
│   ├── launcher.py                     # 启动器脚本
│   ├── castep_conductivity_calculator.py    # 介电函数电导率计算器
│   ├── boltzmann_transport_calculator.py    # Boltzmann输运计算器
│   └── README.md                       # 代码使用说明
└── README.md          # 本文件，项目总体说明
```

## 功能介绍

本工具提供以下核心功能：

1. **基于介电函数的电导率计算**
   - 从CASTEP输出的.epsilon文件或导出数据读取介电函数
   - 基于公式 σ(ω)=ω·ε₂·ε₀ 计算光学电导率
   - 通过低频区域线性外推获得直流电导率σ_dc
   - 可视化介电函数和电导率曲线

2. **基于Boltzmann输运理论的电导率计算**
   - 从CASTEP输出的.bands文件读取能带结构
   - 计算不同温度下的电导率张量
   - 分析材料电导率的温度依赖性和各向异性
   - 可视化能带结构、态密度和电导率-温度关系

## 运行环境

- Python 3.6+
- 依赖库：numpy, pandas, matplotlib, scipy, tkinter, pillow

## 使用方法

1. 在CASTEP中完成电子结构计算，获取.epsilon或.bands输出文件
2. 运行启动器：
```
cd 代码
python launcher.py
```
3. 选择合适的计算方法，根据界面提示导入数据并计算

## 背景理论

详细的理论背景和计算步骤请参考[计算步骤.md](计算步骤.md)文件，该文件基于Materials Studio 2024《CASTEP GUIDE》整理，给出了在CASTEP模块中计算晶体电导率的完整专业步骤。

## 参考文献

- Materials Studio CASTEP用户指南 (2024版)
- S. Baroni, et al. "Density-Functional Perturbation Theory for Lattice Dynamics and Dielectric Properties." Rev. Mod. Phys.
- G.K.H. Madsen and D.J. Singh. "BoltzTraP. A code for calculating band-structure dependent quantities." Computer Physics Communications. 