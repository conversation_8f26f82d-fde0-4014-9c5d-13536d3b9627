#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
材料类型分析器

用于自动检测材料类型（金属、半导体、绝缘体）并提供相应的计算策略。

作者：Claude AI助手
日期：2025-01-20
"""

import numpy as np
from enum import Enum
from typing import Tuple, Dict, Any, Optional
import json


class MaterialType(Enum):
    """材料类型枚举"""
    METAL = "metal"
    SEMICONDUCTOR = "semiconductor"
    INSULATOR = "insulator"
    UNKNOWN = "unknown"


class MaterialAnalyzer:
    """材料类型分析器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化材料分析器
        
        参数:
            config: 配置字典
        """
        self.config = config
        self.detection_params = config.get('material_detection', {})
        
    def analyze_material(self, bands: np.ndarray, fermi_energy: float, 
                        n_electrons: float, n_spins: int = 1) -> <PERSON><PERSON>[MaterialType, Dict[str, Any]]:
        """
        分析材料类型
        
        参数:
            bands: 能带数据 [n_bands, n_kpoints]
            fermi_energy: 费米能级 (eV)
            n_electrons: 电子数
            n_spins: 自旋分量数
            
        返回:
            (材料类型, 分析结果字典)
        """
        analysis_result = {
            'fermi_energy': fermi_energy,
            'band_gap': 0.0,
            'vbm': 0.0,  # 价带顶
            'cbm': 0.0,  # 导带底
            'bands_at_fermi': 0,
            'metallic_bands': [],
            'is_fermi_crossing': False,
            'material_properties': {}
        }
        
        # 1. 检测费米能级是否穿过能带
        fermi_crossing, crossing_bands = self._check_fermi_crossing(bands, fermi_energy)
        analysis_result['is_fermi_crossing'] = fermi_crossing
        analysis_result['metallic_bands'] = crossing_bands
        analysis_result['bands_at_fermi'] = len(crossing_bands)
        
        # 2. 估算能带间隙
        band_gap, vbm, cbm = self._estimate_band_gap(bands, fermi_energy, n_electrons, n_spins)
        analysis_result['band_gap'] = band_gap
        analysis_result['vbm'] = vbm
        analysis_result['cbm'] = cbm
        
        # 3. 确定材料类型
        material_type = self._classify_material(fermi_crossing, band_gap, analysis_result)
        
        # 4. 添加材料特性信息
        analysis_result['material_properties'] = self._get_material_properties(material_type, analysis_result)
        
        return material_type, analysis_result
    
    def _check_fermi_crossing(self, bands: np.ndarray, fermi_energy: float) -> Tuple[bool, list]:
        """
        检查费米能级是否穿过能带
        
        返回:
            (是否穿过, 穿过的能带索引列表)
        """
        tolerance = self.detection_params.get('fermi_level_tolerance', 0.01)
        crossing_bands = []
        
        n_bands, n_kpoints = bands.shape
        
        for band_idx in range(n_bands):
            band_min = np.min(bands[band_idx])
            band_max = np.max(bands[band_idx])
            
            # 检查费米能级是否在能带范围内
            if (band_min - tolerance) <= fermi_energy <= (band_max + tolerance):
                crossing_bands.append(band_idx)
        
        return len(crossing_bands) > 0, crossing_bands
    
    def _estimate_band_gap(self, bands: np.ndarray, fermi_energy: float, 
                          n_electrons: float, n_spins: int) -> Tuple[float, float, float]:
        """
        估算能带间隙
        
        返回:
            (带隙, 价带顶, 导带底)
        """
        n_bands, n_kpoints = bands.shape
        
        # 估算价带和导带的分界
        # 对于非自旋极化计算，每个能带可容纳2个电子
        # 对于自旋极化计算，每个能带每个自旋可容纳1个电子
        electrons_per_band = 2 if n_spins == 1 else 1
        occupied_bands = int(n_electrons / electrons_per_band)
        
        if occupied_bands <= 0 or occupied_bands >= n_bands:
            # 无法确定价带导带分界，返回零带隙
            return 0.0, fermi_energy, fermi_energy
        
        # 价带顶：最高占据能带的最大能量
        vbm = np.max(bands[occupied_bands - 1])
        
        # 导带底：最低未占据能带的最小能量
        cbm = np.min(bands[occupied_bands])
        
        # 带隙
        band_gap = max(0.0, cbm - vbm)
        
        return band_gap, vbm, cbm
    
    def _classify_material(self, fermi_crossing: bool, band_gap: float, 
                          analysis_result: Dict[str, Any]) -> MaterialType:
        """
        根据分析结果分类材料类型
        """
        gap_threshold = self.detection_params.get('band_gap_threshold', 0.1)
        semiconductor_range = self.detection_params.get('semiconductor_gap_range', [0.1, 4.0])
        insulator_threshold = self.detection_params.get('insulator_gap_threshold', 4.0)
        
        # 如果费米能级穿过能带，通常是金属
        if fermi_crossing:
            return MaterialType.METAL
        
        # 根据带隙大小分类
        if band_gap < gap_threshold:
            # 小带隙或零带隙，可能是金属或半金属
            return MaterialType.METAL
        elif semiconductor_range[0] <= band_gap <= semiconductor_range[1]:
            # 中等带隙，半导体
            return MaterialType.SEMICONDUCTOR
        elif band_gap > insulator_threshold:
            # 大带隙，绝缘体
            return MaterialType.INSULATOR
        else:
            # 介于半导体和绝缘体之间
            return MaterialType.SEMICONDUCTOR
    
    def _get_material_properties(self, material_type: MaterialType, 
                               analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取材料特性参数
        """
        conductivity_params = self.config.get('conductivity_estimation', {})
        type_params = conductivity_params.get(material_type.value, {})
        
        properties = {
            'typical_conductivity': type_params.get('typical_conductivity', 1e18),
            'relaxation_time': type_params.get('relaxation_time', 1e-14),
            'scaling_factor': type_params.get('scaling_factor', 1.0),
            'calculation_strategy': self._get_calculation_strategy(material_type, analysis_result)
        }
        
        # 为半导体和绝缘体添加激活能因子
        if material_type in [MaterialType.SEMICONDUCTOR, MaterialType.INSULATOR]:
            properties['activation_energy_factor'] = type_params.get('activation_energy_factor', 0.5)
        
        return properties
    
    def _get_calculation_strategy(self, material_type: MaterialType, 
                                analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        根据材料类型确定计算策略
        """
        strategy = {
            'use_fermi_window': True,
            'energy_window_width': 5.0,  # eV
            'temperature_scaling': True,
            'use_alternative_method': False
        }
        
        if material_type == MaterialType.METAL:
            strategy.update({
                'energy_window_width': 2.0,  # 金属使用较窄的能量窗口
                'focus_on_fermi_surface': True,
                'use_band_crossing_optimization': True
            })
        elif material_type == MaterialType.SEMICONDUCTOR:
            strategy.update({
                'energy_window_width': analysis_result['band_gap'] + 2.0,
                'include_thermal_excitation': True,
                'use_gap_correction': True
            })
        elif material_type == MaterialType.INSULATOR:
            strategy.update({
                'energy_window_width': max(10.0, analysis_result['band_gap'] + 5.0),
                'use_alternative_method': True,  # 绝缘体可能需要特殊处理
                'thermal_activation_dominant': True
            })
        
        return strategy
    
    def print_analysis_report(self, material_type: MaterialType, analysis_result: Dict[str, Any]):
        """
        打印材料分析报告
        """
        print("\n" + "="*50)
        print("材料类型分析报告")
        print("="*50)
        
        print(f"材料类型: {self._get_material_type_name(material_type)}")
        print(f"费米能级: {analysis_result['fermi_energy']:.6f} eV")
        
        if analysis_result['is_fermi_crossing']:
            print(f"费米能级穿过能带: 是")
            print(f"穿过的能带数: {analysis_result['bands_at_fermi']}")
            print(f"金属性能带: {analysis_result['metallic_bands']}")
        else:
            print(f"费米能级穿过能带: 否")
        
        print(f"估算带隙: {analysis_result['band_gap']:.6f} eV")
        if analysis_result['band_gap'] > 0:
            print(f"价带顶 (VBM): {analysis_result['vbm']:.6f} eV")
            print(f"导带底 (CBM): {analysis_result['cbm']:.6f} eV")
        
        # 材料特性
        props = analysis_result['material_properties']
        print(f"\n推荐计算参数:")
        print(f"典型电导率: {props['typical_conductivity']:.2e} S/m/s")
        print(f"弛豫时间: {props['relaxation_time']:.2e} s")
        print(f"缩放因子: {props['scaling_factor']:.2e}")
        
        # 计算策略
        strategy = props['calculation_strategy']
        print(f"\n计算策略:")
        print(f"能量窗口宽度: {strategy['energy_window_width']:.2f} eV")
        print(f"使用费米窗口: {strategy['use_fermi_window']}")
        print(f"温度缩放: {strategy['temperature_scaling']}")
        
        print("="*50)
    
    def _get_material_type_name(self, material_type: MaterialType) -> str:
        """获取材料类型的中文名称"""
        names = {
            MaterialType.METAL: "金属",
            MaterialType.SEMICONDUCTOR: "半导体", 
            MaterialType.INSULATOR: "绝缘体",
            MaterialType.UNKNOWN: "未知"
        }
        return names.get(material_type, "未知")
