#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
CASTEP 电导率计算工具启动器 (改进版)

支持配置文件的命令行界面，用于启动电导率计算。
支持自动材料类型检测和自适应计算策略。

使用方法:
    python launcher.py <bands_file> [选项]

作者：Claude AI助手
日期：2025-01-20
"""

import os
import sys
import argparse
import json
import numpy as np

# 添加当前目录到路径以确保可以导入模块
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

# 导入计算模块
try:
    from castep_conductivity_calculator import ConductivityCalculator
except ImportError as e:
    print(f"错误: 无法导入必要的模块: {str(e)}")
    print("请确保castep_conductivity_calculator.py和material_analyzer.py文件位于同一目录下")
    sys.exit(1)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='CASTEP电导率计算工具 (改进版)',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
    python launcher.py Cu.bands
    python launcher.py Cu.bands -t 500
    python launcher.py Cu.bands --temperature 300 --output results.txt
    python launcher.py Cu.bands --config custom_config.json
        """
    )

    parser.add_argument('bands_file',
                        help='CASTEP .bands文件路径')
    parser.add_argument('-t', '--temperature',
                        type=float,
                        default=None,
                        help='计算温度 (K), 如果未指定则使用配置文件中的默认值')
    parser.add_argument('-o', '--output',
                        type=str,
                        default=None,
                        help='输出结果文件路径 (可选)')
    parser.add_argument('-c', '--config',
                        type=str,
                        default='config.json',
                        help='配置文件路径 (默认: config.json)')
    parser.add_argument('--show-analysis',
                        action='store_true',
                        help='显示详细的材料分析报告')

    args = parser.parse_args()

    # 检查输入文件是否存在
    if not os.path.exists(args.bands_file):
        print(f"错误: 文件 '{args.bands_file}' 不存在")
        return 1

    # 检查配置文件
    if not os.path.exists(args.config):
        print(f"警告: 配置文件 '{args.config}' 不存在，将使用默认配置")

    # 创建计算器实例
    try:
        calculator = ConductivityCalculator(config_path=args.config)
    except Exception as e:
        print(f"初始化计算器失败: {str(e)}")
        return 1
    
    # 读取.bands文件
    try:
        print(f"正在读取文件: {args.bands_file}")
        calculator.read_bands_file(args.bands_file)
        print("文件读取成功")
    except Exception as e:
        print(f"读取文件错误: {str(e)}")
        return 1

    # 显示材料分析结果（如果请求）
    if args.show_analysis and calculator.material_analysis:
        print("\n" + "="*60)
        print("详细材料分析报告")
        print("="*60)
        display_detailed_analysis(calculator)

    # 计算电导率
    try:
        temperature = args.temperature
        if temperature is None:
            temperature = calculator.config.get('calculation_parameters', {}).get('default_temperature', 300.0)

        print(f"\n开始计算电导率 (温度: {temperature}K)...")
        sigma_tensor, actual_conductivity = calculator.calculate_conductivity(temperature=temperature)
        print("\n计算完成!")

        # 如果指定了输出文件，保存结果
        if args.output:
            try:
                save_results(args.output, calculator, sigma_tensor, actual_conductivity, temperature)
                print(f"\n结果已保存到: {args.output}")
            except Exception as e:
                print(f"保存结果时出错: {str(e)}")

    except Exception as e:
        print(f"计算错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1

    return 0


def display_detailed_analysis(calculator):
    """显示详细的材料分析信息"""
    analysis = calculator.material_analysis
    if not analysis:
        print("无材料分析数据")
        return

    print(f"材料类型: {calculator.material_type.value}")
    print(f"费米能级: {analysis['fermi_energy']:.6f} eV")
    print(f"估算带隙: {analysis['band_gap']:.6f} eV")

    if analysis['band_gap'] > 0:
        print(f"价带顶 (VBM): {analysis['vbm']:.6f} eV")
        print(f"导带底 (CBM): {analysis['cbm']:.6f} eV")

    print(f"费米能级穿过能带: {'是' if analysis['is_fermi_crossing'] else '否'}")
    if analysis['is_fermi_crossing']:
        print(f"穿过的能带数: {analysis['bands_at_fermi']}")
        print(f"金属性能带: {analysis['metallic_bands']}")

    # 显示推荐的计算参数
    if 'material_properties' in analysis:
        props = analysis['material_properties']
        print(f"\n推荐计算参数:")
        print(f"典型电导率: {props['typical_conductivity']:.2e} S/m/s")
        print(f"弛豫时间: {props['relaxation_time']:.2e} s")
        print(f"缩放因子: {props['scaling_factor']:.2e}")

        if 'calculation_strategy' in props:
            strategy = props['calculation_strategy']
            print(f"\n计算策略:")
            print(f"能量窗口宽度: {strategy['energy_window_width']:.2f} eV")
            print(f"使用费米窗口: {strategy['use_fermi_window']}")
            print(f"温度缩放: {strategy['temperature_scaling']}")


def save_results(output_file, calculator, sigma_tensor, actual_conductivity, temperature):
    """保存计算结果到文件"""
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("CASTEP 电导率计算结果 (改进版)\n")
        f.write("=" * 50 + "\n\n")

        f.write(f"输入文件: {calculator.filename}\n")
        f.write(f"计算温度: {temperature} K\n")
        f.write(f"费米能级: {calculator.fermi_energy:.6f} eV\n")
        f.write(f"能带数: {calculator.n_bands}\n")
        f.write(f"k点数: {calculator.n_kpoints}\n")
        f.write(f"电子数: {calculator.n_electrons}\n")
        f.write(f"自旋分量: {calculator.n_spins}\n\n")

        # 材料分析结果
        if calculator.material_analysis:
            f.write("材料分析结果:\n")
            f.write("-" * 30 + "\n")
            f.write(f"材料类型: {calculator.material_type.value}\n")
            f.write(f"估算带隙: {calculator.material_analysis['band_gap']:.6f} eV\n")
            f.write(f"费米能级穿过能带: {'是' if calculator.material_analysis['is_fermi_crossing'] else '否'}\n")
            if calculator.material_analysis['is_fermi_crossing']:
                f.write(f"穿过的能带数: {calculator.material_analysis['bands_at_fermi']}\n")
            f.write("\n")

        f.write("电导率张量 (σ/τ，单位: S/m/s):\n")
        f.write("-" * 30 + "\n")
        f.write(f"σ_xx = {sigma_tensor[0, 0]:.4e}\n")
        f.write(f"σ_yy = {sigma_tensor[1, 1]:.4e}\n")
        f.write(f"σ_zz = {sigma_tensor[2, 2]:.4e}\n")
        f.write(f"平均电导率 = {np.trace(sigma_tensor)/3:.4e}\n\n")

        # 获取使用的弛豫时间
        if calculator.material_analysis and 'material_properties' in calculator.material_analysis:
            tau_s = calculator.material_analysis['material_properties'].get('relaxation_time', 1e-14)
        else:
            tau_s = calculator.config.get('output_settings', {}).get('default_relaxation_time', 1e-14)

        f.write(f"实际电导率 (使用弛豫时间 τ = {tau_s:.0e} s):\n")
        f.write("-" * 40 + "\n")
        f.write(f"σ_xx = {actual_conductivity[0, 0]:.4e}\n")
        f.write(f"σ_yy = {actual_conductivity[1, 1]:.4e}\n")
        f.write(f"σ_zz = {actual_conductivity[2, 2]:.4e}\n")
        f.write(f"平均电导率 = {np.trace(actual_conductivity)/3:.4e}\n\n")

        # 计算各向异性比
        eigenvalues, _ = np.linalg.eigh(sigma_tensor)
        eigenvalues = np.sort(np.abs(eigenvalues))
        if eigenvalues[0] > 0:
            aniso_ratio = eigenvalues[-1] / eigenvalues[0]
            f.write(f"电导率各向异性比: {aniso_ratio:.2f}\n")


if __name__ == '__main__':
    sys.exit(main())