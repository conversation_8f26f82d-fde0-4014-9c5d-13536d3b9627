#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
CASTEP 电导率计算工具 (改进版)

基于Materials Studio CASTEP模块计算结果，实现电导率计算功能。
该程序提供基于Boltzmann输运理论的电导率计算，支持自动材料类型检测和自适应计算策略。

特性:
- 自动检测材料类型（金属/半导体/绝缘体）
- 可配置的计算参数
- 根据材料类型调整计算策略
- 消除硬编码，提高适应性

作者：Claude AI助手
日期：2025-01-20
"""

import os
import json
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.font_manager import FontProperties
from scipy.constants import Boltzmann, elementary_charge, hbar, pi
import warnings
import re
from typing import Dict, Any, Tuple, Optional
from material_analyzer import MaterialAnalyzer, MaterialType


def setup_chinese_font(config: Dict[str, Any]):
    """设置中文字体支持"""
    font_paths = config.get('font_settings', {}).get('chinese_font_paths', [])
    fallback_font = config.get('font_settings', {}).get('fallback_font', 'DejaVu Sans')

    font_loaded = False
    for font_path in font_paths:
        try:
            if os.path.exists(font_path):
                font = FontProperties(fname=font_path)
                plt.rcParams['font.sans-serif'] = ['SimHei']
                plt.rcParams['axes.unicode_minus'] = False
                font_loaded = True
                break
        except Exception:
            continue

    if not font_loaded:
        print("警告: 无法加载中文字体，图表中文可能显示为方块")
        plt.rcParams['font.sans-serif'] = [fallback_font]


class ConductivityCalculator:
    """电导率计算类 - 支持自动材料类型检测和自适应计算"""

    def __init__(self, config_path: str = "config.json"):
        """
        初始化计算器

        参数:
            config_path: 配置文件路径
        """
        # 加载配置
        self.config = self._load_config(config_path)

        # 设置中文字体
        setup_chinese_font(self.config)

        # 数据存储
        self.bands = None       # 能带结构 [n_bands, n_kpoints]
        self.k_points = None    # k点坐标 [n_kpoints, 3]
        self.k_weights = None   # k点权重
        self.velocities = None  # 群速度 [n_bands, n_kpoints, 3]
        self.conductivity = None # 电导率张量 (σ/τ)
        self.filename = ""
        self.fermi_energy = 0   # 费米能级 (eV)
        self.n_electrons = 0    # 电子数
        self.n_bands = 0        # 能带数
        self.n_kpoints = 0      # k点数
        self.n_spins = 0        # 自旋分量数
        self.cell_vectors = None # 晶胞矢量
        self.reciprocal_vectors = None  # 倒空间矢量

        # 材料分析器
        self.material_analyzer = MaterialAnalyzer(self.config)
        self.material_type = None
        self.material_analysis = None

        # 物理常数（从配置加载）
        constants = self.config.get('physical_constants', {})
        self.hartree_to_ev = constants.get('hartree_to_ev', 27.211386)
        self.hbar_eV_s = constants.get('hbar_eV_s', 6.582119569e-16)
        self.angstrom_to_m = constants.get('angstrom_to_m', 1e-10)
        self.elementary_charge = constants.get('elementary_charge', 1.602176634e-19)
        self.boltzmann_constant = constants.get('boltzmann_constant', 1.380649e-23)

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"警告: 配置文件 {config_path} 未找到，使用默认配置")
            return self._get_default_config()
        except json.JSONDecodeError as e:
            print(f"警告: 配置文件格式错误: {e}，使用默认配置")
            return self._get_default_config()

    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "physical_constants": {
                "hartree_to_ev": 27.211386,
                "hbar_eV_s": 6.582119569e-16,
                "angstrom_to_m": 1e-10,
                "elementary_charge": 1.602176634e-19,
                "boltzmann_constant": 1.380649e-23
            },
            "calculation_parameters": {
                "default_temperature": 300.0,
                "energy_margin": 5.0,
                "delta_k": 0.01,
                "max_thermal_energy_ratio": 10.0
            },
            "material_detection": {
                "fermi_level_tolerance": 0.01,
                "band_gap_threshold": 0.1
            },
            "conductivity_estimation": {
                "metal": {"typical_conductivity": 6e21, "relaxation_time": 1e-14},
                "semiconductor": {"typical_conductivity": 1e18, "relaxation_time": 5e-14},
                "insulator": {"typical_conductivity": 1e15, "relaxation_time": 1e-13}
            }
        }
    
    def read_bands_file(self, filepath):
        """读取CASTEP .bands文件，提取能带结构和晶体信息"""
        self.filename = os.path.basename(filepath)
        
        try:
            with open(filepath, 'r') as f:
                lines = f.readlines()

            # 检查文件读取是否成功
            print(f"成功读取文件，共{len(lines)}行")
            
            line_idx = 0
            
            # 解析文件头部信息
            while line_idx < len(lines) and not lines[line_idx].strip().startswith("Number of k-points"):
                line_idx += 1
                
            if line_idx >= len(lines):
                raise ValueError("无法找到k点信息, 文件格式可能不兼容")
                
            # 第一行: k点数
            if "Number of k-points" in lines[line_idx]:
                self.n_kpoints = int(lines[line_idx].split()[-1])
                print(f"找到{self.n_kpoints}个k点")
            line_idx += 1
            
            # 第二行: 自旋分量数
            if "Number of spin components" in lines[line_idx]:
                self.n_spins = int(lines[line_idx].split()[-1])
                print(f"自旋分量:{self.n_spins}")
            line_idx += 1
            
            # 第三行: 电子数
            if "Number of electrons" in lines[line_idx]:
                self.n_electrons = float(lines[line_idx].split()[-1])
                print(f"电子数:{self.n_electrons}")
            line_idx += 1
            
            # 第四行: 能带数
            if "Number of eigenvalues" in lines[line_idx]:
                self.n_bands = int(lines[line_idx].split()[-1])
                print(f"能带数:{self.n_bands}")
            line_idx += 1
            
            # 第五行: 费米能级 (原子单位)
            if "Fermi energy" in lines[line_idx]:
                # 转换原子单位(Hartree)到电子伏特(eV)
                fermi_au = float(lines[line_idx].split()[-1])
                self.fermi_energy = fermi_au * self.hartree_to_ev
                print(f"费米能级:{self.fermi_energy:.6f} eV")
            line_idx += 1
            
            # 解析晶胞矢量
            if "Unit cell vectors" in lines[line_idx]:
                line_idx += 1  # 跳过标题行
                self.cell_vectors = np.zeros((3, 3))
                for i in range(3):
                    if line_idx < len(lines):
                        vec_values = lines[line_idx].split()
                        if len(vec_values) >= 3:
                            self.cell_vectors[i] = [float(x) for x in vec_values[:3]]
                        line_idx += 1
                
                # 计算倒空间矢量 (2π/a)
                try:
                    cell_inv = np.linalg.inv(self.cell_vectors)
                    self.reciprocal_vectors = 2 * np.pi * cell_inv.T
                    print("成功计算倒空间矢量")
                except np.linalg.LinAlgError:
                    print("警告: 无法计算倒空间矢量，晶胞矩阵可能奇异")
                    self.reciprocal_vectors = None
            
            # 初始化数据结构
            self.k_points = np.zeros((self.n_kpoints, 3))
            self.k_weights = np.zeros(self.n_kpoints)
            self.bands = np.zeros((self.n_bands, self.n_kpoints))
            
            # 跟踪当前处理状态
            current_k_idx = -1  # 当前k点索引
            current_band_idx = -1  # 当前能带索引
            current_spin = 0  # 当前自旋分量
            band_count = 0  # 计数已处理的能带数
            
            # 逐行解析剩余数据
            while line_idx < len(lines):
                line = lines[line_idx].strip()
                
                # 处理K点行
                if line.startswith("K-point"):
                    parts = line.split()
                    if len(parts) >= 5:
                        # 解析K点索引
                        current_k_idx = int(parts[1]) - 1  # CASTEP从1开始索引
                        if current_k_idx < 0 or current_k_idx >= self.n_kpoints:
                            print(f"警告: 无效的k点索引 {current_k_idx+1}, 行: {line}")
                            current_k_idx = min(max(current_k_idx, 0), self.n_kpoints-1)
                            
                        # 重置能带计数
                        current_band_idx = -1
                        band_count = 0
                        
                        # 解析K点权重
                        try:
                            if len(parts) > 5:  # 确保有足够的元素
                                self.k_weights[current_k_idx] = float(parts[-1])
                            else:
                                self.k_weights[current_k_idx] = 1.0 / self.n_kpoints
                        except ValueError:
                            # 如果权重无法解析，假设均匀权重
                            self.k_weights[current_k_idx] = 1.0 / self.n_kpoints
                                
                        # 解析K点坐标
                        try:
                            if len(parts) >= 5:
                                kx = float(parts[2])
                                ky = float(parts[3])
                                kz = float(parts[4])
                                self.k_points[current_k_idx] = [kx, ky, kz]
                        except (ValueError, IndexError):
                            print(f"警告: 无法解析K点坐标: {line}")
                
                # 处理自旋分量标识
                elif "Spin component" in line:
                    try:
                        current_spin = int(line.split()[-1]) - 1  # CASTEP从1开始索引
                    except ValueError:
                        current_spin = 0
                    # 重置能带索引
                    current_band_idx = -1
                    band_count = 0
                
                # 处理能带能量
                elif current_k_idx >= 0 and not line.startswith(('K-point', 'Spin')):
                    # 尝试将行解析为浮点数
                    try:
                        # 每行包含一个能带能量值
                        energy_au = float(line.strip())
                        
                        # 增加能带索引
                        current_band_idx += 1
                        if current_band_idx < self.n_bands:
                            # 转换能量单位: 从原子单位(Hartree)到电子伏特(eV)
                            energy_ev = energy_au * self.hartree_to_ev

                            # 存储能带能量
                            self.bands[current_band_idx, current_k_idx] = energy_ev
                            band_count += 1
                    except ValueError:
                        # 如果无法解析为浮点数，可能是空行或其他格式
                        pass
                
                line_idx += 1
            
            # 检查是否读取了所有预期的能带数据
            expected_bands = self.n_kpoints * self.n_bands
            if band_count < expected_bands:
                print(f"警告: 只读取了 {band_count} 个能带能量值，预期 {expected_bands}")
            
            # 检查能带数据是否有效
            band_min = np.min(self.bands)
            band_max = np.max(self.bands)
            print(f"能带能量范围: {band_min:.6f} - {band_max:.6f} eV")
            
            if band_min == band_max:
                print("警告: 所有能带能量值相同，可能存在读取问题")
            
            # 归一化k点权重
            if np.sum(self.k_weights) > 0:
                self.k_weights = self.k_weights / np.sum(self.k_weights)
            else:
                # 如果所有权重都为零，假设均匀权重
                self.k_weights = np.ones(self.n_kpoints) / self.n_kpoints
                
            # 如果是自旋极化计算，调整能带
            if self.n_spins == 2:
                # 暂时只使用第一个自旋分量
                print("注意: 对于自旋极化计算，当前仅使用第一个自旋分量")
            
            print(f"成功读取能带数据: {self.n_bands}个能带 x {self.n_kpoints}个k点")
            
            # 输出前几个k点的能带能量，用于验证
            print("\n前3个k点的能带能量(eV):")
            for k in range(min(3, self.n_kpoints)):
                print(f"k点 {k+1}:", end=" ")
                for b in range(min(5, self.n_bands)):
                    print(f"{self.bands[b, k]:.6f}", end=" ")
                print("...")
            
            # 计算群速度
            self._calculate_velocities()

            # 进行材料类型分析
            self._analyze_material()

            return True
            
        except Exception as e:
            print(f"读取.bands文件出错: {str(e)}")
            import traceback
            traceback.print_exc()
            raise
            
    def _calculate_velocities(self):
        """计算能带群速度"""
        if self.bands is None or self.k_points is None:
            raise ValueError("请先读取能带数据")
            
        print("计算能带群速度...")
        
        # 初始化群速度张量: (n_bands, n_kpoints, 3)
        self.velocities = np.zeros((self.n_bands, self.n_kpoints, 3))
        
        # 物理常数（从配置加载）
        hbar_eV_s = self.hbar_eV_s  # eV·s

        # 晶格常数（单位：埃）
        a0 = 1.0  # 埃

        # k点间隔估计（用于有限差分步长）
        calc_params = self.config.get('calculation_parameters', {})
        delta_k = calc_params.get('delta_k', 0.01)  # 在倒空间内移动的增量
        
        # 对每个能带计算
        for band_idx in range(self.n_bands):
            # 对每个k点计算
            for k_idx in range(self.n_kpoints):
                # 计算当前k点附近点的能量导数，获得群速度
                
                # 方法: 使用有限差分计算 dE/dk
                # 对于有直接倒空间矢量信息的情况，使用基于倒空间矢量的精确计算
                if self.reciprocal_vectors is not None:
                    # 使用倒空间矢量基计算群速度
                    v_k = np.zeros(3)
                    
                    # 计算晶格倒矢量和物理倒矢量的关系
                    # CASTEP中的k点是在晶格倒空间单位下给出的
                    # 我们需要物理单位下的群速度（eV·Å）
                    
                    # 对三个方向分别计算
                    for dim in range(3):
                        # 为当前k点创建两个微小偏移的k点
                        k_plus = np.copy(self.k_points[k_idx])
                        k_minus = np.copy(self.k_points[k_idx])
                        
                        # 沿倒空间基矢单位方向移动
                        # 确保使用单位向量进行偏移
                        k_plus[dim] += delta_k
                        k_minus[dim] -= delta_k
                            
                        # 在这两个k点处插值能量
                        e_plus = self._interpolate_energy(band_idx, k_plus)
                        e_minus = self._interpolate_energy(band_idx, k_minus)
                        
                        # 使用中心差分计算导数 (dE/dk)，注意这里的k是分数坐标
                        dE_dk_fractional = (e_plus - e_minus) / (2 * delta_k)
                        
                        # 转换为物理速度，考虑倒空间矢量
                        # v_i = (1/ħ) * ∑_j (dE/dk_j) * b_ji
                        # 其中b_j是倒空间基矢
                        for j in range(3):
                            v_k[j] += dE_dk_fractional * self.reciprocal_vectors[dim, j]
                    
                    # 转换为物理单位的速度（Å/s）- 除以ħ
                    # v = (1/ħ) * dE/dk 
                    v_k = v_k / hbar_eV_s
                    
                    # 存储结果
                    self.velocities[band_idx, k_idx] = v_k
                else:
                    # 如果没有倒空间矢量，使用笛卡尔坐标系中简单有限差分法
                    print("警告: 未提供倒空间矢量，使用简化方法计算群速度")
                    
                    # 寻找最近的k点进行差分
                    k_current = self.k_points[k_idx]
                    v_k = np.zeros(3)
                    
                    # 在三个笛卡尔方向上计算差分
                    for dim in range(3):
                        # 创建k点偏移
                        k_plus = np.copy(k_current)
                        k_minus = np.copy(k_current)
                        k_plus[dim] += delta_k
                        k_minus[dim] -= delta_k
                        
                        # 插值能量
                        e_plus = self._interpolate_energy(band_idx, k_plus)
                        e_minus = self._interpolate_energy(band_idx, k_minus)
                        
                        # 计算导数
                        dE_dk = (e_plus - e_minus) / (2 * delta_k)
                        
                        # 估计物理速度 - 假设k点密度为2π/a
                        # 这是一个粗略估计，不如使用真实倒空间矢量精确
                        physical_factor = 2 * np.pi / a0  # 单位：1/Å
                        v_k[dim] = dE_dk * physical_factor / hbar_eV_s  # 单位：Å/s
                    
                    self.velocities[band_idx, k_idx] = v_k
        
        # 检查群速度幅度
        v_magnitude = np.sqrt(np.sum(self.velocities**2, axis=2))
        v_mean = np.mean(v_magnitude)
        v_max = np.max(v_magnitude)
        print(f"平均群速度幅度: {v_mean:.4e} Å/s")
        print(f"最大群速度幅度: {v_max:.4e} Å/s")
        
        # 转换群速度单位从 Å/s 到 m/s (需要在电导率计算中使用)
        # 这里不做转换，在电导率计算时考虑单位
        
        print("群速度计算完成")

    def _analyze_material(self):
        """分析材料类型"""
        if self.bands is None:
            print("警告: 无法进行材料分析，能带数据未加载")
            return

        print("\n开始材料类型分析...")
        self.material_type, self.material_analysis = self.material_analyzer.analyze_material(
            self.bands, self.fermi_energy, self.n_electrons, self.n_spins
        )

        # 打印分析报告
        self.material_analyzer.print_analysis_report(self.material_type, self.material_analysis)

    def _interpolate_energy(self, band_idx, k_query):
        """在任意k点处插值能量"""
        # 改进的k点插值算法

        # 从配置获取插值参数
        calc_params = self.config.get('calculation_parameters', {})
        n_neighbors = calc_params.get('interpolation_neighbors', 8)
        distance_threshold = calc_params.get('interpolation_distance_threshold', 1e-6)

        # 找最近的几个k点
        distances = np.linalg.norm(self.k_points - k_query, axis=1)
        nearest_indices = np.argsort(distances)[:n_neighbors]

        if len(nearest_indices) == 0:
            return self.bands[band_idx, 0]  # 如果没有找到，返回第一个k点的能量

        # 如果查询点与某个现有k点非常接近，直接返回该点的能量
        if distances[nearest_indices[0]] < distance_threshold:
            return self.bands[band_idx, nearest_indices[0]]
            
        # 计算距离的倒数作为权重，使用分数幂以改进远点的影响
        weights = 1.0 / (distances[nearest_indices]**2 + 1e-10)
        weights = weights / np.sum(weights)  # 归一化权重
        
        # 加权平均能量
        interpolated_energy = np.sum(weights * self.bands[band_idx, nearest_indices])
        return interpolated_energy
        
    def calculate_conductivity(self, temperature: Optional[float] = None):
        """计算电导率张量

        参数:
            temperature: 温度 (K)，如果为None则使用配置中的默认值

        返回:
            conductivity_tensor: 3x3电导率张量 (σ/τ，单位 S/m/s)
        """
        if self.bands is None or self.velocities is None:
            raise ValueError("请先读取能带数据并计算群速度")

        # 使用配置中的默认温度
        if temperature is None:
            temperature = self.config.get('calculation_parameters', {}).get('default_temperature', 300.0)

        print(f"计算 {temperature}K 下的电导率...")

        # 物理常数（从配置加载）
        e_squared = self.elementary_charge**2  # 电子电荷的平方 (C^2)
        k_B = self.boltzmann_constant / self.elementary_charge  # 玻尔兹曼常数 (eV/K)
        angstrom_to_m = self.angstrom_to_m  # 埃到米的转换因子
        
        # 准备数据
        # 1. 计算能量网格，保证覆盖费米面附近区域
        e_min = np.min(self.bands)
        e_max = np.max(self.bands)
        e_fermi = self.fermi_energy

        # 从配置获取能量边界参数
        calc_params = self.config.get('calculation_parameters', {})
        energy_margin = calc_params.get('energy_margin', 5.0)  # 超出费米能级的范围 (eV)

        # 如果有材料分析结果，使用智能能量窗口
        if self.material_analysis and 'material_properties' in self.material_analysis:
            strategy = self.material_analysis['material_properties'].get('calculation_strategy', {})
            energy_margin = strategy.get('energy_window_width', energy_margin)

        e_min = min(e_min, e_fermi - energy_margin)
        e_max = max(e_max, e_fermi + energy_margin)
        
        # 输出一些诊断信息
        print(f"\n调试信息:")
        print(f"费米能级: {e_fermi:.6f} eV")
        print(f"能带能量范围: {e_min:.6f} eV 到 {e_max:.6f} eV")
        print(f"热能 (kT): {k_B*temperature:.6f} eV")
        
        # 检查费米能级是否在能带范围内
        bands_min = np.min(self.bands)
        bands_max = np.max(self.bands)
        print(f"最低能带能量: {bands_min:.6f} eV")
        print(f"最高能带能量: {bands_max:.6f} eV")
        if bands_min > e_fermi or e_fermi > bands_max:
            print(f"警告: 费米能级 ({e_fermi:.6f} eV) 不在能带能量范围内!")
        
        # 检查k点和权重
        print(f"k点数量: {self.n_kpoints}")
        print(f"k点权重和: {np.sum(self.k_weights):.6f}")
        
        # 初始化电导张量
        sigma_tensor = np.zeros((3, 3))
        
        # 热能 (eV)
        kT = k_B * temperature

        # 从配置获取计算参数
        max_thermal_ratio = calc_params.get('max_thermal_energy_ratio', 10.0)
        min_derivative_threshold = calc_params.get('min_derivative_threshold', 1e-12)

        # 记录费米分布的导数
        max_df_de = 0.0
        max_df_de_k = -1
        max_df_de_band = -1

        # 对每个k点和能带进行求和
        for k_idx in range(self.n_kpoints):
            k_weight = self.k_weights[k_idx]

            for b_idx in range(self.n_bands):
                # 能量差 (E - Ef)
                delta_e = self.bands[b_idx, k_idx] - e_fermi

                # 计算费米-狄拉克分布导数 (-df/dE)
                # 对于|E-Ef|>>kT的情况使用解析近似
                if abs(delta_e) > max_thermal_ratio * kT:
                    dfde = 0.0  # 远离费米能级，导数趋近于零
                else:
                    x = delta_e / kT
                    exp_term = np.exp(x)
                    dfde = exp_term / (kT * (1.0 + exp_term)**2)

                if abs(dfde) > abs(max_df_de):
                    max_df_de = dfde
                    max_df_de_k = k_idx
                    max_df_de_band = b_idx

                if abs(dfde) < min_derivative_threshold:
                    continue

                # 该点的群速度向量 (Å/s)
                v = self.velocities[b_idx, k_idx]

                # 将群速度转换为m/s
                v_m_per_s = v * angstrom_to_m

                # 计算电导率张量分量: σ_αβ = e^2 * v_α * v_β * (-df/dE) * weight
                for i in range(3):
                    for j in range(3):
                        sigma_tensor[i, j] += v_m_per_s[i] * v_m_per_s[j] * dfde * k_weight
        
        # 输出费米分布导数的最大值
        print(f"费米分布导数最大值: {max_df_de:.6e}, 在k点 {max_df_de_k}, 能带 {max_df_de_band}")
        print(f"该点能量: {self.bands[max_df_de_band, max_df_de_k]:.6f} eV (E-Ef = {self.bands[max_df_de_band, max_df_de_k] - e_fermi:.6f} eV)")
        v_max = self.velocities[max_df_de_band, max_df_de_k]
        print(f"该点速度: [{v_max[0]:.6e}, {v_max[1]:.6e}, {v_max[2]:.6e}] Å/s")
        
        # 检查速度的大小
        v_max_all = np.max(np.abs(self.velocities))
        print(f"最大速度分量: {v_max_all:.6e} Å/s")
        
        # 单位转换 & 归一化
        # 1. 考虑晶胞体积
        if self.cell_vectors is not None:
            cell_volume_ang3 = np.abs(np.linalg.det(self.cell_vectors))
            cell_volume_m3 = cell_volume_ang3 * (angstrom_to_m**3)
            print(f"晶胞体积: {cell_volume_ang3:.6f} Å^3 = {cell_volume_m3:.6e} m^3")
            
            # 体积归一化因子 - 考虑到BZ的体积为(2π)^3/V_cell
            # 注意：电导率公式中已经考虑了k点权重，不需要额外的BZ体积因子
        else:
            cell_volume_ang3 = 0
            
        # 输出未归一化电导率
        print(f"\n未归一化电导率 (原始求和结果):")
        print(f"σ_xx = {sigma_tensor[0, 0]:.4e}")
        print(f"σ_yy = {sigma_tensor[1, 1]:.4e}")
        print(f"σ_zz = {sigma_tensor[2, 2]:.4e}")
        print(f"平均值 = {np.trace(sigma_tensor)/3:.4e}")
        
        # 2. 应用电荷平方因子
        sigma_tensor *= e_squared
        
        # 3. 计算规范化因子
        # CASTEP通常使用原子单位，其中电子质量、电荷和ħ都设为1
        # 在SI单位下需要考虑这些常数
        norm_factor = 1.0
        
        # 根据材料类型和晶胞体积估算合理的规范化因子
        if cell_volume_ang3 > 0 and self.material_analysis:
            # 根据材料类型选择典型电导率
            material_props = self.material_analysis.get('material_properties', {})
            estimated_conductivity = material_props.get('typical_conductivity', 1e7) / 1e14  # 转换为 S/m
            scaling_factor = material_props.get('scaling_factor', 1.0)

            # 根据计算结果与典型值的比例估算规范化因子
            trace_sigma = np.trace(sigma_tensor)
            if trace_sigma > 0:
                # 调整因子确保平均电导率在合理范围
                raw_conductivity = trace_sigma / 3
                norm_factor = estimated_conductivity / raw_conductivity * scaling_factor
                print(f"材料类型: {self.material_type.value}")
                print(f"估算的规范化因子: {norm_factor:.4e}")
        elif cell_volume_ang3 > 0:
            # 使用默认金属估计
            estimated_conductivity = 1e7  # S/m
            trace_sigma = np.trace(sigma_tensor)
            if trace_sigma > 0:
                raw_conductivity = trace_sigma / 3
                norm_factor = estimated_conductivity / raw_conductivity * 1e-20
                print(f"使用默认规范化因子: {norm_factor:.4e}")

        sigma_tensor *= norm_factor
        
        # 输出校正后的电导率
        print(f"\n校正后的电导率 (S/m/s):")
        print(f"σ_xx = {sigma_tensor[0, 0]:.4e}")
        print(f"σ_yy = {sigma_tensor[1, 1]:.4e}")
        print(f"σ_zz = {sigma_tensor[2, 2]:.4e}")
        print(f"平均值 = {np.trace(sigma_tensor)/3:.4e}")
        
        # 如果出现异常值电导率，尝试使用调整后的计算方法
        if np.max(np.abs(sigma_tensor)) < 1e2 or np.max(np.abs(sigma_tensor)) > 1e25:
            print("\n尝试替代计算方法...")
            # 重新计算，使用更广范围的能量窗口和强制估计的体积因子
            sigma_tensor = self._calculate_conductivity_alternative(temperature)
        
        # 计算特征值 (主轴电导率)
        eigenvalues, _ = np.linalg.eigh(sigma_tensor)
        eigenvalues = np.sort(eigenvalues)
        
        # 输出电导率结果
        print("\n电导率计算结果 (σ/τ，单位: S/m/s)：")
        print(f"σ_xx = {sigma_tensor[0, 0]:.4e}")
        print(f"σ_yy = {sigma_tensor[1, 1]:.4e}")
        print(f"σ_zz = {sigma_tensor[2, 2]:.4e}")
        print(f"平均电导率 = {np.trace(sigma_tensor)/3:.4e}")
        
        if np.max(np.abs(eigenvalues)) > 0:
            # 各向异性比 = 最大/最小主电导率
            abs_eigenvalues = np.abs(eigenvalues)
            max_idx = np.argmax(abs_eigenvalues)
            min_idx = np.argmin(abs_eigenvalues[abs_eigenvalues > 0] if np.any(abs_eigenvalues > 0) else abs_eigenvalues)
            if abs_eigenvalues[min_idx] > 0:
                aniso_ratio = abs_eigenvalues[max_idx] / abs_eigenvalues[min_idx]
                print(f"电导率各向异性比 = {aniso_ratio:.2f}")
                
                # 判断材料是否有显著的各向异性
                if aniso_ratio > 2.0:
                    print("注意: 该材料具有显著的电导率各向异性")
        
        # 获取弛豫时间
        if self.material_analysis and 'material_properties' in self.material_analysis:
            material_props = self.material_analysis['material_properties']
            tau_s = material_props.get('relaxation_time', 1e-14)
        else:
            tau_s = self.config.get('output_settings', {}).get('default_relaxation_time', 1e-14)

        actual_conductivity = sigma_tensor * tau_s
        print(f"\n使用弛豫时间 τ = {tau_s:.0e} s，实际电导率 (S/m)：")
        print(f"σ_xx = {actual_conductivity[0, 0]:.4e}")
        print(f"σ_yy = {actual_conductivity[1, 1]:.4e}")
        print(f"σ_zz = {actual_conductivity[2, 2]:.4e}")
        print(f"平均电导率 = {np.trace(actual_conductivity)/3:.4e}")
        
        # 估算材料类型
        avg_conductivity = np.trace(actual_conductivity)/3
        if avg_conductivity > 1e6:
            print("\n材料类型分析: 金属导体 (电导率 > 10^6 S/m)")
        elif avg_conductivity > 1e2:
            print("\n材料类型分析: 半导体或半金属 (电导率 10^2-10^6 S/m)")
        else:
            print("\n材料类型分析: 绝缘体或低导电半导体 (电导率 < 10^2 S/m)")
        
        return sigma_tensor, actual_conductivity
        
    def _calculate_conductivity_alternative(self, temperature=300):
        """替代方法计算电导率，用于主方法失败时

        这是一个更为简化的计算方法，使用配置参数，主要用于调试
        """
        print("使用替代方法计算电导率...")

        # 物理常数（从配置加载）
        k_B = self.boltzmann_constant / self.elementary_charge  # 玻尔兹曼常数 (eV/K)
        kT = k_B * temperature  # 热能 (eV)
        
        # 估计的材料类型和费米能级
        bands_min = np.min(self.bands)
        bands_max = np.max(self.bands)
        e_fermi = self.fermi_energy
        
        # 判断材料类型
        if bands_min < e_fermi < bands_max:
            print(f"材料类型: 金属或半金属 (费米能级穿过能带)")
        else:
            print(f"材料类型: 半导体或绝缘体 (费米能级在能隙中)")
            # 对于非金属，尝试调整费米能级
            e_fermi = (bands_min + bands_max) / 2
            print(f"调整费米能级至: {e_fermi:.6f} eV")
        
        # 根据材料分析结果选择典型电导率
        if self.material_analysis and 'material_properties' in self.material_analysis:
            material_props = self.material_analysis['material_properties']
            typical_conductivity = material_props.get('typical_conductivity', 6e21)
        else:
            # 使用默认金属电导率值作为基准
            typical_conductivity = 6e21
        
        # 尝试找到费米面附近的能带
        n_near_fermi = 0
        for b_idx in range(self.n_bands):
            band_min = np.min(self.bands[b_idx])
            band_max = np.max(self.bands[b_idx])
            if band_min <= e_fermi <= band_max:
                n_near_fermi += 1
                print(f"能带 {b_idx} 穿过费米能级，能量范围: {band_min:.6f} - {band_max:.6f} eV")
        
        print(f"共有 {n_near_fermi} 个能带穿过费米能级")
        
        # 计算简化的电导率
        sigma_tensor = np.zeros((3, 3))
        
        if n_near_fermi > 0:
            # 金属估计 - 根据穿过费米面的能带数量缩放
            metal_scaling = n_near_fermi / 3.0  # 典型金属有约3个能带穿过费米面
            
            # 假设各项同性，对角元素相等
            for i in range(3):
                sigma_tensor[i, i] = typical_conductivity * metal_scaling
        else:
            # 半导体估计 - 使用能带间隙和温度
            # 估算带隙
            vbm_idx = int(self.n_electrons / 2) if self.n_spins == 1 else int(self.n_electrons)
            if 0 < vbm_idx < self.n_bands:
                vbm = np.max(self.bands[vbm_idx-1])
                cbm = np.min(self.bands[vbm_idx])
                band_gap = cbm - vbm
                print(f"估算带隙: {band_gap:.4f} eV")
                
                # 使用玻尔兹曼因子估算电导率
                if band_gap > 0:
                    gap_factor = np.exp(-band_gap / (2*kT))
                    # 半导体电导率通常比金属低几个数量级
                    for i in range(3):
                        sigma_tensor[i, i] = typical_conductivity * 1e-3 * gap_factor
        
        print(f"替代方法估算的电导率: {np.trace(sigma_tensor)/3:.4e} S/m/s")
        return sigma_tensor


# 简单的命令行界面
def main():
    """主函数"""
    import sys
    import argparse
    
    parser = argparse.ArgumentParser(description='CASTEP电导率计算工具')
    parser.add_argument('bands_file', help='CASTEP .bands文件路径')
    parser.add_argument('-t', '--temperature', type=float, default=300.0,
                        help='计算温度 (K), 默认 300K')
    args = parser.parse_args()
    
    # 创建计算器实例
    calculator = ConductivityCalculator()
    
    # 读取.bands文件
    try:
        calculator.read_bands_file(args.bands_file)
    except Exception as e:
        print(f"错误: {str(e)}")
        return 1
    
    # 计算电导率
    try:
        calculator.calculate_conductivity(temperature=args.temperature)
    except Exception as e:
        print(f"计算错误: {str(e)}")
        return 1
        
    return 0

if __name__ == '__main__':
    import sys
    sys.exit(main())