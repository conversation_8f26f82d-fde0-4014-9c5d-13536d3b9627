#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
CASTEP 电导率计算结果管理器

统一管理所有计算结果，提供清晰的文件组织和查看功能

作者：Claude AI助手
日期：2025-01-20
"""

import os
import json
import shutil
import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

class ResultManager:
    """统一结果管理器"""
    
    def __init__(self, base_dir: str = "CASTEP_Results"):
        """
        初始化结果管理器
        
        参数:
            base_dir: 结果根目录
        """
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(exist_ok=True)
        
        # 创建标准目录结构
        self.dirs = {
            'calculations': self.base_dir / 'calculations',
            'visualizations': self.base_dir / 'visualizations', 
            'reports': self.base_dir / 'reports',
            'data': self.base_dir / 'data',
            'logs': self.base_dir / 'logs'
        }
        
        for dir_path in self.dirs.values():
            dir_path.mkdir(exist_ok=True)
        
        self.index_file = self.base_dir / 'results_index.json'
        self.load_index()
    
    def load_index(self):
        """加载结果索引"""
        if self.index_file.exists():
            with open(self.index_file, 'r', encoding='utf-8') as f:
                self.index = json.load(f)
        else:
            self.index = {
                'version': '1.0',
                'created': datetime.datetime.now().isoformat(),
                'calculations': {}
            }
    
    def save_index(self):
        """保存结果索引"""
        with open(self.index_file, 'w', encoding='utf-8') as f:
            json.dump(self.index, f, ensure_ascii=False, indent=2)
    
    def create_calculation_session(self, material_name: str, description: str = "") -> str:
        """
        创建新的计算会话
        
        参数:
            material_name: 材料名称
            description: 计算描述
            
        返回:
            session_id: 会话ID
        """
        timestamp = datetime.datetime.now()
        session_id = f"{material_name}_{timestamp.strftime('%Y%m%d_%H%M%S')}"
        
        # 创建会话目录
        session_dir = self.dirs['calculations'] / session_id
        session_dir.mkdir(exist_ok=True)
        
        # 创建子目录
        subdirs = ['input', 'output', 'plots', 'reports']
        for subdir in subdirs:
            (session_dir / subdir).mkdir(exist_ok=True)
        
        # 记录到索引
        self.index['calculations'][session_id] = {
            'material_name': material_name,
            'description': description,
            'created': timestamp.isoformat(),
            'status': 'created',
            'files': {
                'input': [],
                'output': [],
                'plots': [],
                'reports': []
            }
        }
        
        self.save_index()
        return session_id
    
    def add_file(self, session_id: str, file_path: str, file_type: str, 
                 description: str = "", copy_file: bool = True) -> str:
        """
        添加文件到会话
        
        参数:
            session_id: 会话ID
            file_path: 文件路径
            file_type: 文件类型 (input/output/plots/reports)
            description: 文件描述
            copy_file: 是否复制文件
            
        返回:
            新文件路径
        """
        if session_id not in self.index['calculations']:
            raise ValueError(f"会话 {session_id} 不存在")
        
        session_dir = self.dirs['calculations'] / session_id
        target_dir = session_dir / file_type
        
        source_path = Path(file_path)
        target_path = target_dir / source_path.name
        
        if copy_file and source_path.exists():
            shutil.copy2(source_path, target_path)
        
        # 更新索引
        file_info = {
            'filename': source_path.name,
            'path': str(target_path.relative_to(self.base_dir)),
            'description': description,
            'added': datetime.datetime.now().isoformat(),
            'size': target_path.stat().st_size if target_path.exists() else 0
        }
        
        self.index['calculations'][session_id]['files'][file_type].append(file_info)
        self.save_index()
        
        return str(target_path)
    
    def update_session_status(self, session_id: str, status: str, 
                            results: Optional[Dict[str, Any]] = None):
        """更新会话状态"""
        if session_id not in self.index['calculations']:
            raise ValueError(f"会话 {session_id} 不存在")
        
        self.index['calculations'][session_id]['status'] = status
        self.index['calculations'][session_id]['updated'] = datetime.datetime.now().isoformat()
        
        if results:
            self.index['calculations'][session_id]['results'] = results
        
        self.save_index()
    
    def get_session_info(self, session_id: str) -> Dict[str, Any]:
        """获取会话信息"""
        if session_id not in self.index['calculations']:
            raise ValueError(f"会话 {session_id} 不存在")
        
        return self.index['calculations'][session_id]
    
    def list_sessions(self, material_name: Optional[str] = None) -> List[Dict[str, Any]]:
        """列出所有会话"""
        sessions = []
        for session_id, info in self.index['calculations'].items():
            if material_name is None or info['material_name'] == material_name:
                session_info = info.copy()
                session_info['session_id'] = session_id
                sessions.append(session_info)
        
        # 按创建时间排序
        sessions.sort(key=lambda x: x['created'], reverse=True)
        return sessions
    
    def get_session_directory(self, session_id: str) -> Path:
        """获取会话目录路径"""
        return self.dirs['calculations'] / session_id
    
    def generate_session_report(self, session_id: str) -> str:
        """生成会话报告"""
        if session_id not in self.index['calculations']:
            raise ValueError(f"会话 {session_id} 不存在")
        
        session_info = self.index['calculations'][session_id]
        session_dir = self.get_session_directory(session_id)
        
        # 生成HTML报告
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>CASTEP 计算报告 - {session_info['material_name']}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }}
        .header {{ background: #f4f4f4; padding: 20px; border-radius: 5px; margin-bottom: 20px; }}
        .section {{ margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }}
        .file-list {{ list-style: none; padding: 0; }}
        .file-item {{ padding: 10px; margin: 5px 0; background: #f9f9f9; border-radius: 3px; }}
        .status {{ padding: 5px 10px; border-radius: 3px; color: white; }}
        .status-completed {{ background: #27ae60; }}
        .status-running {{ background: #f39c12; }}
        .status-error {{ background: #e74c3c; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>CASTEP 电导率计算报告</h1>
        <h2>材料: {session_info['material_name']}</h2>
        <p><strong>会话ID:</strong> {session_id}</p>
        <p><strong>创建时间:</strong> {session_info['created']}</p>
        <p><strong>状态:</strong> <span class="status status-{session_info['status']}">{session_info['status']}</span></p>
        <p><strong>描述:</strong> {session_info.get('description', '无')}</p>
    </div>
        """
        
        # 添加文件列表
        for file_type, files in session_info['files'].items():
            if files:
                html_content += f"""
    <div class="section">
        <h3>{file_type.upper()} 文件</h3>
        <ul class="file-list">
                """
                for file_info in files:
                    html_content += f"""
            <li class="file-item">
                <strong>{file_info['filename']}</strong><br>
                描述: {file_info.get('description', '无')}<br>
                大小: {file_info.get('size', 0)} bytes<br>
                添加时间: {file_info.get('added', '未知')}
            </li>
                    """
                html_content += """
        </ul>
    </div>
                """
        
        # 添加结果信息
        if 'results' in session_info:
            results = session_info['results']
            html_content += f"""
    <div class="section">
        <h3>计算结果</h3>
        <p><strong>材料类型:</strong> {results.get('material_type', '未知')}</p>
        <p><strong>费米能级:</strong> {results.get('fermi_energy', 'N/A')} eV</p>
        <p><strong>带隙:</strong> {results.get('band_gap', 'N/A')} eV</p>
        <p><strong>平均电导率:</strong> {results.get('avg_conductivity', 'N/A')} S/m</p>
    </div>
            """
        
        html_content += """
</body>
</html>
        """
        
        # 保存报告
        report_path = session_dir / 'reports' / 'session_report.html'
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        return str(report_path)
    
    def cleanup_old_sessions(self, days: int = 30):
        """清理旧的会话"""
        cutoff_date = datetime.datetime.now() - datetime.timedelta(days=days)
        
        sessions_to_remove = []
        for session_id, info in self.index['calculations'].items():
            created_date = datetime.datetime.fromisoformat(info['created'])
            if created_date < cutoff_date:
                sessions_to_remove.append(session_id)
        
        for session_id in sessions_to_remove:
            session_dir = self.get_session_directory(session_id)
            if session_dir.exists():
                shutil.rmtree(session_dir)
            del self.index['calculations'][session_id]
        
        self.save_index()
        return len(sessions_to_remove)
    
    def export_session(self, session_id: str, export_path: str):
        """导出会话到指定路径"""
        session_dir = self.get_session_directory(session_id)
        if not session_dir.exists():
            raise ValueError(f"会话目录不存在: {session_dir}")
        
        export_path = Path(export_path)
        shutil.copytree(session_dir, export_path / session_id)
        
        # 导出会话信息
        session_info = self.get_session_info(session_id)
        info_file = export_path / session_id / 'session_info.json'
        with open(info_file, 'w', encoding='utf-8') as f:
            json.dump(session_info, f, ensure_ascii=False, indent=2)
