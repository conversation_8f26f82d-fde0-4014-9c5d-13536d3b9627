# CASTEP 电导率计算工具可视化指南

## 概述

本指南详细介绍CASTEP电导率计算工具的可视化功能，帮助用户充分利用图表和报告功能来分析计算结果。

## 🎯 快速开始

### 最简单的可视化
```bash
# 显示所有图表
python launcher.py Cu.bands --plot
```

### 生成完整报告
```bash
# 生成HTML报告并保存所有图表
python launcher.py Cu.bands --report --save-plots my_results
```

## 📊 可视化功能详解

### 1. 能带结构图 (`--plot-bands`)

**功能：**
- 显示完整的能带结构
- 标记费米能级位置（红色虚线）
- 高亮金属性能带（红色实线）
- 显示材料信息文本框

**使用场景：**
- 验证费米能级位置是否合理
- 识别金属性能带
- 检查能带计算质量

**示例：**
```bash
python launcher.py Cu.bands --plot-bands --save-plots band_analysis
```

**图表解读：**
- 蓝色线条：普通能带
- 红色线条：穿过费米面的金属性能带
- 红色虚线：费米能级位置
- 文本框：材料类型、带隙、穿过费米面的能带数

### 2. 电导率张量图 (`--plot-conductivity`)

**功能：**
- 4个子图全面展示电导率特性
- 热图显示完整张量矩阵
- 对角元素柱状图比较
- 各向异性分析

**子图说明：**

**左上：电导率张量热图 (σ/τ)**
- 颜色深浅表示数值大小
- 白色数字显示精确值
- 对角元素通常最大

**右上：实际电导率热图 (S/m)**
- 考虑弛豫时间后的实际值
- 用于与实验数据对比

**左下：对角元素比较**
- σ_xx, σ_yy, σ_zz的直观对比
- 对数刻度显示
- 显示各向异性程度

**右下：主轴分布饼图**
- 电导率张量的特征值分布
- 各向异性比数值
- 主轴贡献百分比

**示例：**
```bash
python launcher.py Cu.bands --plot-conductivity -t 500 --save-plots conductivity_500K
```

### 3. 材料分析图 (`--plot-analysis`)

**功能：**
- 4个子图展示材料特性分析
- 费米能级与能带关系
- 材料类型识别结果
- 能带间隙分析
- 推荐参数显示

**子图说明：**

**左上：费米能级与能带分布**
- 水平条形图显示每个能带的能量范围
- 红色：金属性能带
- 蓝色：非金属性能带
- 绿色虚线：费米能级

**右上：材料类型饼图**
- 显示识别的材料类型
- 金属/半导体/绝缘体

**左下：能带间隙分析**
- 带隙大小的分类显示
- 小/中/大带隙的范围标识
- 当前材料的带隙位置

**右下：推荐参数对比**
- 典型电导率、弛豫时间、缩放因子
- 对数刻度显示
- 实际数值标注

**示例：**
```bash
python launcher.py Cu.bands --plot-analysis --show-analysis
```

### 4. HTML综合报告 (`--report`)

**功能：**
- 生成完整的HTML报告
- 包含所有图表和数据表格
- 专业的报告格式
- 可直接用于文档和演示

**报告内容：**

1. **报告头部**
   - 文件名和生成时间
   - 材料基本信息表格

2. **材料分析结果**
   - 材料分析图表
   - 详细分析数据表格

3. **能带结构**
   - 能带结构图
   - 费米能级和能带信息

4. **电导率计算结果**
   - 电导率张量图表
   - 完整的数值表格
   - 各向异性分析

5. **计算说明**
   - 方法介绍
   - 结果解释

**示例：**
```bash
python launcher.py Cu.bands --report --save-plots final_report
# 打开 final_report/report.html 查看
```

## 🔧 高级使用技巧

### 1. 批量可视化
```bash
# 对多个文件生成报告
for file in *.bands; do
    python launcher.py "$file" --report --save-plots "${file%.bands}_report"
done
```

### 2. 温度对比分析
```bash
# 不同温度下的电导率对比
python launcher.py Cu.bands -t 300 --plot-conductivity --save-plots temp_300K
python launcher.py Cu.bands -t 500 --plot-conductivity --save-plots temp_500K
python launcher.py Cu.bands -t 800 --plot-conductivity --save-plots temp_800K
```

### 3. 配置优化可视化
```bash
# 使用不同配置的结果对比
python launcher.py Cu.bands --config config1.json --plot --save-plots config1_results
python launcher.py Cu.bands --config config2.json --plot --save-plots config2_results
```

### 4. 完整分析流程
```bash
# 推荐的完整分析流程
python launcher.py Cu.bands --show-analysis --report --save-plots complete_analysis
```

## 📈 图表解读指南

### 能带结构图解读
- **金属特征**：费米能级穿过多个能带
- **半导体特征**：费米能级在小带隙中
- **绝缘体特征**：费米能级在大带隙中

### 电导率张量解读
- **对角元素**：主要导电方向
- **非对角元素**：耦合效应
- **各向异性比**：>2表示显著各向异性

### 材料分析解读
- **带隙大小**：决定材料类型
- **费米穿越**：金属性的直接证据
- **推荐参数**：优化计算精度

## ⚠️ 注意事项

### 1. 字体问题
- 可能出现中文字体警告
- 不影响图表生成和保存
- 可通过配置文件调整字体路径

### 2. 图表质量
- 保存的PNG图片为高分辨率（300 DPI）
- 适合用于论文和报告
- HTML报告可直接打印

### 3. 内存使用
- 大型系统可能需要较多内存
- 可选择性生成特定图表
- 避免同时显示过多图表

## 🎨 自定义可视化

### 修改图表样式
可以通过修改 `visualization.py` 中的参数来自定义：
- 颜色方案
- 图表大小
- 字体设置
- 数据精度

### 添加新的图表类型
可以扩展 `ConductivityVisualizer` 类来添加：
- 温度依赖性图表
- 能量分辨电导率
- 3D可视化
- 动画效果

## 📝 输出文件说明

### 图片文件
- `band_structure.png`：能带结构图
- `conductivity_tensor.png`：电导率张量图
- `material_analysis.png`：材料分析图

### HTML报告
- `report.html`：完整的可视化报告
- 包含所有图片的引用
- 可离线查看
- 支持打印和分享

## 🚀 最佳实践

1. **首次使用**：先用 `--show-analysis` 了解材料特性
2. **快速检查**：使用 `--plot-bands` 验证能带结构
3. **详细分析**：使用 `--report` 生成完整报告
4. **结果保存**：始终使用 `--save-plots` 保存图表
5. **文档记录**：HTML报告可直接用于研究记录

通过合理使用这些可视化功能，您可以更深入地理解CASTEP电导率计算的结果，并生成专业的分析报告。
